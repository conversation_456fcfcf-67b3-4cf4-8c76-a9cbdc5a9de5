<?php
$page_title = "Produk Kami";
$page_description = "Jelajahi berbagai produk air berkualitas tinggi dari Agen Air Fazza - air isi ulang dan air kemasan dengan harga terjangkau";

require_once 'includes/header.php';

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Search and filter
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitizeInput($_GET['sort']) : 'newest';

// Build query conditions
$where_conditions = ["status = 'aktif'"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(nama_produk LIKE ? OR deskripsi LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$where_clause = implode(' AND ', $where_conditions);

// Sort options
$order_by = match($sort) {
    'name_asc' => 'nama_produk ASC',
    'name_desc' => 'nama_produk DESC',
    'price_asc' => 'harga ASC',
    'price_desc' => 'harga DESC',
    'oldest' => 'created_at ASC',
    default => 'created_at DESC'
};

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM produk WHERE $where_clause";
$total_result = fetchSingle($count_sql, $params);
$total_products = $total_result['total'];
$total_pages = ceil($total_products / $limit);

// Get products
$sql = "SELECT * FROM produk WHERE $where_clause ORDER BY $order_by LIMIT $limit OFFSET $offset";
$products = fetchAll($sql, $params);
?>

<!-- Page Header -->
<section class="relative py-20 bg-gradient-to-r from-primary-600 to-secondary-600 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-pattern"></div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-10 left-10 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce-soft"></div>
    <div class="absolute top-32 right-20 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 left-32 w-8 h-8 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 2s;"></div>
    
    <div class="relative z-10 container mx-auto px-4 text-center text-white">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6" data-animate="fade-in">
            Produk Kami
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" data-animate="slide-up">
            Pilihan lengkap produk air berkualitas tinggi untuk memenuhi kebutuhan hidrasi keluarga Anda
        </p>
        
        <!-- Breadcrumb -->
        <nav class="mt-8" data-animate="slide-up">
            <ol class="flex items-center justify-center space-x-2 text-blue-100">
                <li><a href="<?php echo SITE_URL; ?>" class="hover:text-white transition-colors">Beranda</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-white">Produk</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Products Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Search and Filter Bar -->
        <div class="bg-white rounded-2xl shadow-soft p-6 mb-12" data-animate="slide-up">
            <form method="GET" class="flex flex-col md:flex-row gap-4 items-center">
                <!-- Search Input -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="Cari produk..." 
                               class="input-field pl-12 w-full">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <!-- Sort Dropdown -->
                <div class="w-full md:w-auto">
                    <select name="sort" class="select-field w-full md:w-48">
                        <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>Terbaru</option>
                        <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>Terlama</option>
                        <option value="name_asc" <?php echo $sort === 'name_asc' ? 'selected' : ''; ?>>Nama A-Z</option>
                        <option value="name_desc" <?php echo $sort === 'name_desc' ? 'selected' : ''; ?>>Nama Z-A</option>
                        <option value="price_asc" <?php echo $sort === 'price_asc' ? 'selected' : ''; ?>>Harga Terendah</option>
                        <option value="price_desc" <?php echo $sort === 'price_desc' ? 'selected' : ''; ?>>Harga Tertinggi</option>
                    </select>
                </div>
                
                <!-- Search Button -->
                <button type="submit" class="btn-primary w-full md:w-auto">
                    <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari
                </button>
                
                <!-- Reset Button -->
                <?php if (!empty($search) || $sort !== 'newest'): ?>
                    <a href="<?php echo SITE_URL; ?>/produk.php" class="btn-secondary w-full md:w-auto">
                        Reset
                    </a>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Results Info -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8" data-animate="slide-up">
            <div class="text-gray-600 mb-4 md:mb-0">
                Menampilkan <?php echo min($offset + 1, $total_products); ?>-<?php echo min($offset + $limit, $total_products); ?> 
                dari <?php echo $total_products; ?> produk
                <?php if (!empty($search)): ?>
                    untuk "<strong><?php echo htmlspecialchars($search); ?></strong>"
                <?php endif; ?>
            </div>
            
            <!-- View Toggle (Grid/List) -->
            <div class="flex items-center space-x-2">
                <span class="text-gray-600 text-sm">Tampilan:</span>
                <button id="grid-view" class="p-2 rounded-lg bg-primary-100 text-primary-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                </button>
                <button id="list-view" class="p-2 rounded-lg hover:bg-gray-100 text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Products Grid -->
        <?php if ($products): ?>
            <div id="products-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
                <?php foreach ($products as $index => $product): ?>
                    <div class="product-card card-product hover-lift" data-animate="slide-up" style="animation-delay: <?php echo $index * 0.1; ?>s;">
                        <!-- Product Image -->
                        <div class="aspect-w-1 aspect-h-1 mb-4 overflow-hidden rounded-xl relative group">
                            <?php if ($product['gambar_produk']): ?>
                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo $product['gambar_produk']; ?>" 
                                     alt="<?php echo htmlspecialchars($product['nama_produk']); ?>" 
                                     class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 8.172V5L8 4z"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Quick Action Overlay -->
                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <a href="<?php echo SITE_URL; ?>/kontak.php?produk=<?php echo urlencode($product['nama_produk']); ?>" 
                                   class="bg-white text-primary-600 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    Pesan Sekarang
                                </a>
                            </div>
                        </div>
                        
                        <!-- Product Info -->
                        <div class="space-y-3">
                            <h3 class="text-lg font-heading font-semibold text-gray-900 line-clamp-2">
                                <?php echo htmlspecialchars($product['nama_produk']); ?>
                            </h3>
                            
                            <p class="text-gray-600 text-sm line-clamp-3 leading-relaxed">
                                <?php echo htmlspecialchars($product['deskripsi']); ?>
                            </p>
                            
                            <!-- Price and Action -->
                            <div class="flex items-center justify-between pt-2">
                                <div class="text-2xl font-bold text-primary-600">
                                    <?php echo formatCurrency($product['harga']); ?>
                                </div>
                                <a href="<?php echo SITE_URL; ?>/kontak.php?produk=<?php echo urlencode($product['nama_produk']); ?>" 
                                   class="btn-primary text-sm px-4 py-2">
                                    Pesan
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="flex justify-center" data-animate="slide-up">
                    <nav class="flex items-center space-x-2">
                        <!-- Previous Button -->
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&sort=<?php echo $sort; ?>" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        <?php endif; ?>
                        
                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&sort=<?php echo $sort; ?>" 
                               class="px-4 py-2 border rounded-lg transition-colors <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <!-- Next Button -->
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&sort=<?php echo $sort; ?>" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-20" data-animate="fade-in">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-heading font-semibold text-gray-900 mb-4">
                    Produk Tidak Ditemukan
                </h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    <?php if (!empty($search)): ?>
                        Tidak ada produk yang cocok dengan pencarian "<?php echo htmlspecialchars($search); ?>". 
                        Coba kata kunci lain atau hapus filter.
                    <?php else: ?>
                        Belum ada produk yang tersedia saat ini. Silakan kembali lagi nanti.
                    <?php endif; ?>
                </p>
                
                <?php if (!empty($search)): ?>
                    <a href="<?php echo SITE_URL; ?>/produk.php" class="btn-primary">
                        Lihat Semua Produk
                    </a>
                <?php else: ?>
                    <a href="<?php echo SITE_URL; ?>" class="btn-primary">
                        Kembali ke Beranda
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
$additional_js = "
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const productsContainer = document.getElementById('products-container');
    
    if (gridView && listView && productsContainer) {
        gridView.addEventListener('click', function() {
            productsContainer.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12';
            gridView.className = 'p-2 rounded-lg bg-primary-100 text-primary-600';
            listView.className = 'p-2 rounded-lg hover:bg-gray-100 text-gray-600';
        });
        
        listView.addEventListener('click', function() {
            productsContainer.className = 'space-y-6 mb-12';
            listView.className = 'p-2 rounded-lg bg-primary-100 text-primary-600';
            gridView.className = 'p-2 rounded-lg hover:bg-gray-100 text-gray-600';
            
            // Update product cards for list view
            const productCards = productsContainer.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.className = 'product-card bg-white rounded-2xl shadow-soft hover:shadow-medium p-6 transition-all duration-300 border border-gray-100 flex flex-col md:flex-row gap-6';
            });
        });
    }
    
    // Auto-submit form on sort change
    const sortSelect = document.querySelector('select[name=\"sort\"]');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
";

require_once 'includes/footer.php';
?>
