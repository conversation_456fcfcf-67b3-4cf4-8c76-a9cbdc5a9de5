<?php
require_once '../config/config.php';

// Check if admin is logged in
requireAdminLogin();

$action = $_GET['download'] ?? $_GET['delete'] ?? '';

if (!empty($_GET['download'])) {
    // Download backup file
    $filename = sanitizeInput($_GET['download']);
    $filepath = BACKUP_PATH . '/' . $filename;
    
    if (file_exists($filepath) && pathinfo($filename, PATHINFO_EXTENSION) === 'sql') {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filepath));
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        
        readfile($filepath);
        exit();
    } else {
        header('Location: pengaturan.php?action=backup&error=file_not_found');
        exit();
    }
}

if (!empty($_GET['delete'])) {
    // Delete backup file
    if (verifyCSRFToken($_GET['token'] ?? '')) {
        $filename = sanitizeInput($_GET['delete']);
        $filepath = BACKUP_PATH . '/' . $filename;
        
        if (file_exists($filepath) && pathinfo($filename, PATHINFO_EXTENSION) === 'sql') {
            if (unlink($filepath)) {
                header('Location: pengaturan.php?action=backup&success=file_deleted');
            } else {
                header('Location: pengaturan.php?action=backup&error=delete_failed');
            }
        } else {
            header('Location: pengaturan.php?action=backup&error=file_not_found');
        }
    } else {
        header('Location: pengaturan.php?action=backup&error=invalid_token');
    }
    exit();
}

// If no action, redirect to settings
header('Location: pengaturan.php?action=backup');
exit();
?>
