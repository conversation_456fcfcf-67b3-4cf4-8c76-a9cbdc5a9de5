<?php
$page_title = "Galeri & Testimoni";
$page_description = "Lihat galeri foto kegiatan dan testimoni pelanggan Agen Air Fazza - bukti nyata kualitas pelayanan kami";

require_once 'includes/header.php';

// Get filter parameters
$kategori = isset($_GET['kategori']) ? sanitizeInput($_GET['kategori']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12; // Items per page for gallery
$offset = ($page - 1) * $limit;

// Build gallery query
$where_conditions = ["status = 'aktif'"];
$params = [];

if (!empty($kategori)) {
    $where_conditions[] = "kategori = ?";
    $params[] = $kategori;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM galeri WHERE $where_clause";
$total_result = fetchSingle($count_sql, $params);
$total_images = $total_result['total'];
$total_pages = ceil($total_images / $limit);

// Get gallery images
$sql = "SELECT * FROM galeri WHERE $where_clause ORDER BY tanggal_upload DESC LIMIT $limit OFFSET $offset";
$gallery_images = fetchAll($sql, $params);

// Get categories for filter
$categories = fetchAll("SELECT DISTINCT kategori FROM galeri WHERE status = 'aktif' ORDER BY kategori");

// Get testimonials
$testimonials = fetchAll("SELECT * FROM testimoni WHERE status = 'aktif' ORDER BY created_at DESC");
?>

<!-- Page Header -->
<section class="relative py-20 bg-gradient-to-r from-primary-600 to-secondary-600 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-pattern"></div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-10 left-10 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce-soft"></div>
    <div class="absolute top-32 right-20 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 left-32 w-8 h-8 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 2s;"></div>
    
    <div class="relative z-10 container mx-auto px-4 text-center text-white">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6" data-animate="fade-in">
            Galeri & Testimoni
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" data-animate="slide-up">
            Lihat aktivitas kami dan dengarkan cerita dari pelanggan yang puas dengan layanan kami
        </p>
        
        <!-- Breadcrumb -->
        <nav class="mt-8" data-animate="slide-up">
            <ol class="flex items-center justify-center space-x-2 text-blue-100">
                <li><a href="<?php echo SITE_URL; ?>" class="hover:text-white transition-colors">Beranda</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-white">Galeri</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Gallery Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16" data-animate="slide-up">
            <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-6">
                Galeri <span class="text-gradient">Kegiatan</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Dokumentasi kegiatan, fasilitas, dan proses produksi air berkualitas di Agen Air Fazza
            </p>
        </div>
        
        <!-- Filter Categories -->
        <div class="flex flex-wrap justify-center gap-4 mb-12" data-animate="slide-up">
            <a href="<?php echo SITE_URL; ?>/galeri.php" 
               class="px-6 py-3 rounded-full font-medium transition-all duration-300 <?php echo empty($kategori) ? 'bg-primary-500 text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-primary-50 hover:text-primary-600'; ?>">
                Semua
            </a>
            <?php foreach ($categories as $cat): ?>
                <a href="<?php echo SITE_URL; ?>/galeri.php?kategori=<?php echo urlencode($cat['kategori']); ?>" 
                   class="px-6 py-3 rounded-full font-medium transition-all duration-300 <?php echo $kategori === $cat['kategori'] ? 'bg-primary-500 text-white shadow-lg' : 'bg-white text-gray-600 hover:bg-primary-50 hover:text-primary-600'; ?>">
                    <?php echo ucfirst($cat['kategori']); ?>
                </a>
            <?php endforeach; ?>
        </div>
        
        <!-- Results Info -->
        <?php if (!empty($kategori)): ?>
            <div class="text-center mb-8" data-animate="slide-up">
                <p class="text-gray-600">
                    Menampilkan <?php echo $total_images; ?> foto untuk kategori 
                    "<strong><?php echo ucfirst($kategori); ?></strong>"
                </p>
            </div>
        <?php endif; ?>
        
        <!-- Gallery Grid -->
        <?php if ($gallery_images): ?>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
                <?php foreach ($gallery_images as $index => $image): ?>
                    <div class="gallery-item group cursor-pointer" 
                         data-animate="slide-up" 
                         style="animation-delay: <?php echo $index * 0.1; ?>s;"
                         data-lightbox="gallery"
                         data-src="<?php echo UPLOADS_URL; ?>/gallery/<?php echo $image['nama_file']; ?>"
                         data-title="<?php echo htmlspecialchars($image['judul_gambar']); ?>"
                         data-description="<?php echo htmlspecialchars($image['deskripsi']); ?>">
                        
                        <div class="relative aspect-square rounded-xl overflow-hidden bg-gray-200">
                            <img src="<?php echo UPLOADS_URL; ?>/gallery/<?php echo $image['nama_file']; ?>" 
                                 alt="<?php echo htmlspecialchars($image['judul_gambar']); ?>" 
                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            
                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center text-white">
                                    <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    <p class="text-sm font-medium">Lihat Detail</p>
                                </div>
                            </div>
                            
                            <!-- Category Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="px-3 py-1 bg-white bg-opacity-90 text-gray-700 text-xs font-medium rounded-full">
                                    <?php echo ucfirst($image['kategori']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Image Info -->
                        <div class="mt-4">
                            <h3 class="font-semibold text-gray-900 mb-1 line-clamp-1">
                                <?php echo htmlspecialchars($image['judul_gambar']); ?>
                            </h3>
                            <?php if ($image['deskripsi']): ?>
                                <p class="text-gray-600 text-sm line-clamp-2">
                                    <?php echo htmlspecialchars($image['deskripsi']); ?>
                                </p>
                            <?php endif; ?>
                            <p class="text-gray-400 text-xs mt-2">
                                <?php echo formatDate($image['tanggal_upload']); ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="flex justify-center" data-animate="slide-up">
                    <nav class="flex items-center space-x-2">
                        <!-- Previous Button -->
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&kategori=<?php echo urlencode($kategori); ?>" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        <?php endif; ?>
                        
                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);
                        
                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                            <a href="?page=<?php echo $i; ?>&kategori=<?php echo urlencode($kategori); ?>" 
                               class="px-4 py-2 border rounded-lg transition-colors <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <!-- Next Button -->
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&kategori=<?php echo urlencode($kategori); ?>" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        <?php endif; ?>
                    </nav>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-20" data-animate="fade-in">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-heading font-semibold text-gray-900 mb-4">
                    Belum Ada Foto
                </h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    <?php if (!empty($kategori)): ?>
                        Belum ada foto untuk kategori "<?php echo ucfirst($kategori); ?>". 
                        Coba kategori lain atau lihat semua foto.
                    <?php else: ?>
                        Belum ada foto galeri yang tersedia saat ini. Silakan kembali lagi nanti.
                    <?php endif; ?>
                </p>
                
                <?php if (!empty($kategori)): ?>
                    <a href="<?php echo SITE_URL; ?>/galeri.php" class="btn-primary">
                        Lihat Semua Foto
                    </a>
                <?php else: ?>
                    <a href="<?php echo SITE_URL; ?>" class="btn-primary">
                        Kembali ke Beranda
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <!-- Section Header -->
        <div class="text-center mb-16" data-animate="slide-up">
            <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-6">
                Testimoni <span class="text-gradient">Pelanggan</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Dengarkan langsung dari pelanggan setia kami tentang pengalaman menggunakan layanan Agen Air Fazza
            </p>
        </div>
        
        <?php if ($testimonials): ?>
            <!-- Testimonials Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <?php foreach ($testimonials as $index => $testimonial): ?>
                    <div class="testimonial-card card-floating" data-animate="slide-up" style="animation-delay: <?php echo $index * 0.2; ?>s;">
                        <!-- Customer Info -->
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 rounded-full overflow-hidden mr-4 flex-shrink-0">
                                <?php if ($testimonial['foto_pelanggan']): ?>
                                    <img src="<?php echo UPLOADS_URL; ?>/testimonials/<?php echo $testimonial['foto_pelanggan']; ?>" 
                                         alt="<?php echo htmlspecialchars($testimonial['nama_pelanggan']); ?>" 
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-r from-primary-400 to-secondary-400 flex items-center justify-center">
                                        <span class="text-white font-semibold text-xl">
                                            <?php echo strtoupper(substr($testimonial['nama_pelanggan'], 0, 1)); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">
                                    <?php echo htmlspecialchars($testimonial['nama_pelanggan']); ?>
                                </h4>
                                
                                <!-- Rating Stars -->
                                <div class="flex items-center">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <svg class="w-4 h-4 <?php echo $i <= $testimonial['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" 
                                             fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    <?php endfor; ?>
                                    <span class="ml-2 text-sm text-gray-500">(<?php echo $testimonial['rating']; ?>/5)</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Testimonial Text -->
                        <blockquote class="text-gray-600 italic leading-relaxed mb-4">
                            "<?php echo htmlspecialchars($testimonial['isi_testimoni']); ?>"
                        </blockquote>
                        
                        <!-- Date -->
                        <div class="text-gray-400 text-sm">
                            <?php echo formatDate($testimonial['created_at']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Testimonial Stats -->
            <div class="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8" data-animate="slide-up">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                    <div>
                        <div class="text-4xl font-bold text-primary-600 mb-2"><?php echo count($testimonials); ?></div>
                        <div class="text-gray-600">Total Testimoni</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary-600 mb-2">
                            <?php 
                            $avg_rating = 0;
                            if ($testimonials) {
                                $total_rating = array_sum(array_column($testimonials, 'rating'));
                                $avg_rating = round($total_rating / count($testimonials), 1);
                            }
                            echo $avg_rating;
                            ?>
                        </div>
                        <div class="text-gray-600">Rating Rata-rata</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold text-primary-600 mb-2">98%</div>
                        <div class="text-gray-600">Kepuasan Pelanggan</div>
                    </div>
                </div>
            </div>
            
        <?php else: ?>
            <!-- Empty State for Testimonials -->
            <div class="text-center py-20" data-animate="fade-in">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-heading font-semibold text-gray-900 mb-4">
                    Belum Ada Testimoni
                </h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    Belum ada testimoni dari pelanggan. Jadilah yang pertama memberikan testimoni!
                </p>
                <a href="<?php echo SITE_URL; ?>/kontak.php" class="btn-primary">
                    Hubungi Kami
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<?php
$additional_js = "
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced Lightbox functionality
    const galleryItems = document.querySelectorAll('[data-lightbox=\"gallery\"]');
    
    galleryItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const src = this.dataset.src;
            const title = this.dataset.title;
            const description = this.dataset.description;
            
            showEnhancedLightbox(src, title, description, index, galleryItems);
        });
    });
});

function showEnhancedLightbox(src, title, description, currentIndex, allItems) {
    const lightbox = document.createElement('div');
    lightbox.className = 'fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 p-4';
    lightbox.innerHTML = `
        <div class=\"relative max-w-6xl max-h-full w-full\">
            <!-- Close Button -->
            <button class=\"absolute top-4 right-4 text-white text-2xl hover:text-gray-300 z-10\" onclick=\"closeLightbox(this)\">
                <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>
                </svg>
            </button>
            
            <!-- Navigation Buttons -->
            ${allItems.length > 1 ? `
                <button class=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300\" onclick=\"navigateLightbox(-1)\">
                    <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>
                    </svg>
                </button>
                <button class=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300\" onclick=\"navigateLightbox(1)\">
                    <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>
                    </svg>
                </button>
            ` : ''}
            
            <!-- Image Container -->
            <div class=\"flex flex-col items-center justify-center h-full\">
                <img src=\"${src}\" alt=\"${title}\" class=\"max-w-full max-h-[70vh] object-contain rounded-lg shadow-2xl\" id=\"lightbox-image\">
                
                <!-- Image Info -->
                <div class=\"text-center mt-6 text-white max-w-2xl\">
                    <h3 class=\"text-2xl font-semibold mb-2\" id=\"lightbox-title\">${title}</h3>
                    ${description ? `<p class=\"text-gray-300\" id=\"lightbox-description\">${description}</p>` : ''}
                    ${allItems.length > 1 ? `<p class=\"text-gray-400 text-sm mt-4\">${currentIndex + 1} dari ${allItems.length}</p>` : ''}
                </div>
            </div>
        </div>
    `;
    
    // Store current index and items for navigation
    lightbox.currentIndex = currentIndex;
    lightbox.allItems = allItems;
    
    document.body.appendChild(lightbox);
    
    // Close on click outside
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox(lightbox);
        }
    });
    
    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox(lightbox);
        } else if (e.key === 'ArrowLeft') {
            navigateLightbox(-1);
        } else if (e.key === 'ArrowRight') {
            navigateLightbox(1);
        }
    });
}

function navigateLightbox(direction) {
    const lightbox = document.querySelector('.fixed.inset-0.bg-black');
    if (!lightbox || !lightbox.allItems) return;
    
    const newIndex = lightbox.currentIndex + direction;
    if (newIndex < 0 || newIndex >= lightbox.allItems.length) return;
    
    const newItem = lightbox.allItems[newIndex];
    const newSrc = newItem.dataset.src;
    const newTitle = newItem.dataset.title;
    const newDescription = newItem.dataset.description;
    
    // Update image and info
    const image = document.getElementById('lightbox-image');
    const title = document.getElementById('lightbox-title');
    const description = document.getElementById('lightbox-description');
    
    image.src = newSrc;
    image.alt = newTitle;
    title.textContent = newTitle;
    if (description) {
        description.textContent = newDescription || '';
    }
    
    lightbox.currentIndex = newIndex;
    
    // Update counter
    const counter = lightbox.querySelector('.text-gray-400.text-sm');
    if (counter) {
        counter.textContent = `${newIndex + 1} dari ${lightbox.allItems.length}`;
    }
}

function closeLightbox(element) {
    const lightbox = element.closest('.fixed');
    if (lightbox) {
        lightbox.remove();
    }
}
</script>
";

require_once 'includes/footer.php';
?>
