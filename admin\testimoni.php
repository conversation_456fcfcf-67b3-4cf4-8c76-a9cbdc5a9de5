<?php
$page_title = "Manajemen Testimoni";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500">Manajemen Testimoni</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        $nama_pelanggan = sanitizeInput($_POST['nama_pelanggan'] ?? '');
        $isi_testimoni = sanitizeInput($_POST['isi_testimoni'] ?? '');
        $rating = (int)($_POST['rating'] ?? 5);
        $status = sanitizeInput($_POST['status'] ?? 'aktif');
        
        // Validation
        $errors = [];
        if (empty($nama_pelanggan)) $errors[] = 'Nama pelanggan wajib diisi';
        if (empty($isi_testimoni)) $errors[] = 'Isi testimoni wajib diisi';
        if ($rating < 1 || $rating > 5) $errors[] = 'Rating harus antara 1-5';
        
        if (empty($errors)) {
            $data = [
                'nama_pelanggan' => $nama_pelanggan,
                'isi_testimoni' => $isi_testimoni,
                'rating' => $rating,
                'status' => $status
            ];
            
            // Handle photo upload
            if (isset($_FILES['foto_pelanggan']) && $_FILES['foto_pelanggan']['error'] === UPLOAD_ERR_OK) {
                $upload_result = uploadFile($_FILES['foto_pelanggan'], UPLOAD_TESTIMONIAL_PATH);
                if ($upload_result['success']) {
                    $data['foto_pelanggan'] = $upload_result['filename'];
                } else {
                    $errors[] = $upload_result['message'];
                }
            }
            
            if (empty($errors)) {
                if ($action === 'add') {
                    if (insertData('testimoni', $data)) {
                        $success_message = 'Testimoni berhasil ditambahkan!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal menambahkan testimoni.';
                    }
                } elseif ($action === 'edit' && $id) {
                    // Delete old photo if new one uploaded
                    if (isset($data['foto_pelanggan'])) {
                        $old_testimonial = fetchSingle("SELECT foto_pelanggan FROM testimoni WHERE id_testimoni = ?", [$id]);
                        if ($old_testimonial && $old_testimonial['foto_pelanggan']) {
                            deleteFile(UPLOAD_TESTIMONIAL_PATH . '/' . $old_testimonial['foto_pelanggan']);
                        }
                    }
                    
                    if (updateData('testimoni', $data, 'id_testimoni = ?', [$id])) {
                        $success_message = 'Testimoni berhasil diperbarui!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal memperbarui testimoni.';
                    }
                }
            } else {
                $error_message = implode('<br>', $errors);
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    $testimonial = fetchSingle("SELECT * FROM testimoni WHERE id_testimoni = ?", [$id]);
    if ($testimonial) {
        // Delete photo file
        if ($testimonial['foto_pelanggan']) {
            deleteFile(UPLOAD_TESTIMONIAL_PATH . '/' . $testimonial['foto_pelanggan']);
        }
        
        if (deleteData('testimoni', 'id_testimoni = ?', [$id])) {
            $success_message = 'Testimoni berhasil dihapus!';
        } else {
            $error_message = 'Gagal menghapus testimoni.';
        }
    }
    $action = 'list';
}

// Get testimonial data for edit
$testimonial_data = null;
if ($action === 'edit' && $id) {
    $testimonial_data = fetchSingle("SELECT * FROM testimoni WHERE id_testimoni = ?", [$id]);
    if (!$testimonial_data) {
        $error_message = 'Testimoni tidak ditemukan.';
        $action = 'list';
    }
}

// Get testimonials list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $rating_filter = $_GET['rating'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(nama_pelanggan LIKE ? OR isi_testimoni LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($rating_filter)) {
        $where_conditions[] = "rating = ?";
        $params[] = $rating_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM testimoni $where_clause";
    $total_testimonials = fetchSingle($count_sql, $params)['total'];
    $total_pages = ceil($total_testimonials / $limit);
    
    // Get testimonials
    $sql = "SELECT * FROM testimoni $where_clause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
    $testimonials = fetchAll($sql, $params);
    
    // Get rating statistics
    $rating_stats = fetchAll("SELECT rating, COUNT(*) as count FROM testimoni GROUP BY rating ORDER BY rating DESC");
    $avg_rating = fetchSingle("SELECT AVG(rating) as avg_rating FROM testimoni WHERE status = 'aktif'")['avg_rating'] ?? 0;
}
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Testimonials List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Testimoni Pelanggan</h2>
                    <p class="text-gray-600 mt-1">Kelola testimoni dan ulasan dari pelanggan</p>
                </div>
                
                <!-- Rating Summary -->
                <div class="flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-yellow-600"><?php echo number_format($avg_rating, 1); ?></div>
                        <div class="text-sm text-gray-600">Rating Rata-rata</div>
                        <div class="flex items-center justify-center mt-1">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <svg class="w-4 h-4 <?php echo $i <= round($avg_rating) ? 'text-yellow-400' : 'text-gray-300'; ?>" 
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <a href="?action=add" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Testimoni
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="p-6 border-b border-gray-100">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Cari testimoni..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="w-full md:w-48">
                    <select name="rating" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Semua Rating</option>
                        <option value="5" <?php echo $rating_filter === '5' ? 'selected' : ''; ?>>⭐⭐⭐⭐⭐ (5)</option>
                        <option value="4" <?php echo $rating_filter === '4' ? 'selected' : ''; ?>>⭐⭐⭐⭐ (4)</option>
                        <option value="3" <?php echo $rating_filter === '3' ? 'selected' : ''; ?>>⭐⭐⭐ (3)</option>
                        <option value="2" <?php echo $rating_filter === '2' ? 'selected' : ''; ?>>⭐⭐ (2)</option>
                        <option value="1" <?php echo $rating_filter === '1' ? 'selected' : ''; ?>>⭐ (1)</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Filter
                </button>
                <?php if (!empty($search) || !empty($rating_filter)): ?>
                    <a href="?" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Reset
                    </a>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Testimonials Grid -->
        <div class="p-6">
            <?php if ($testimonials): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <?php foreach ($testimonials as $testimonial): ?>
                        <div class="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-sm transition-shadow">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <!-- Customer Photo -->
                                    <div class="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                                        <?php if ($testimonial['foto_pelanggan']): ?>
                                            <img src="<?php echo UPLOADS_URL; ?>/testimonials/<?php echo $testimonial['foto_pelanggan']; ?>" 
                                                 alt="<?php echo htmlspecialchars($testimonial['nama_pelanggan']); ?>" 
                                                 class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <span class="text-gray-500 font-semibold text-lg">
                                                <?php echo strtoupper(substr($testimonial['nama_pelanggan'], 0, 1)); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div>
                                        <h3 class="font-semibold text-gray-900">
                                            <?php echo htmlspecialchars($testimonial['nama_pelanggan']); ?>
                                        </h3>
                                        <div class="flex items-center">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <svg class="w-4 h-4 <?php echo $i <= $testimonial['rating'] ? 'text-yellow-400' : 'text-gray-300'; ?>" 
                                                     fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                </svg>
                                            <?php endfor; ?>
                                            <span class="ml-2 text-sm text-gray-600">(<?php echo $testimonial['rating']; ?>/5)</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Status Badge -->
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $testimonial['status'] === 'aktif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo ucfirst($testimonial['status']); ?>
                                </span>
                            </div>
                            
                            <!-- Testimonial Content -->
                            <blockquote class="text-gray-600 italic mb-4 leading-relaxed">
                                "<?php echo htmlspecialchars($testimonial['isi_testimoni']); ?>"
                            </blockquote>
                            
                            <!-- Footer -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                                <div class="text-sm text-gray-500">
                                    <?php echo formatDate($testimonial['created_at']); ?>
                                </div>
                                
                                <!-- Actions -->
                                <div class="flex items-center space-x-2">
                                    <a href="?action=edit&id=<?php echo $testimonial['id_testimoni']; ?>" 
                                       class="text-primary-600 hover:text-primary-900 p-1 rounded hover:bg-primary-50 transition-colors"
                                       data-tooltip="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <a href="?action=delete&id=<?php echo $testimonial['id_testimoni']; ?>" 
                                       class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                                       data-tooltip="Hapus"
                                       data-confirm-delete="Yakin ingin menghapus testimoni dari <?php echo htmlspecialchars($testimonial['nama_pelanggan']); ?>?">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="flex items-center justify-between border-t border-gray-100 pt-6">
                        <div class="text-sm text-gray-700">
                            Menampilkan <?php echo min($offset + 1, $total_testimonials); ?>-<?php echo min($offset + $limit, $total_testimonials); ?> 
                            dari <?php echo $total_testimonials; ?> testimoni
                        </div>
                        <div class="flex space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&rating=<?php echo urlencode($rating_filter); ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&rating=<?php echo urlencode($rating_filter); ?>" 
                                   class="px-3 py-2 border rounded-lg <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white border-gray-300 hover:bg-gray-50'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&rating=<?php echo urlencode($rating_filter); ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Testimoni</h3>
                    <p class="text-gray-600 mb-6">
                        <?php if (!empty($search) || !empty($rating_filter)): ?>
                            Tidak ada testimoni yang cocok dengan filter yang dipilih.
                        <?php else: ?>
                            Mulai dengan menambahkan testimoni pertama dari pelanggan.
                        <?php endif; ?>
                    </p>
                    <?php if (empty($search) && empty($rating_filter)): ?>
                        <a href="?action=add" class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Tambah Testimoni Pertama
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo $action === 'add' ? 'Tambah Testimoni Baru' : 'Edit Testimoni'; ?>
                    </h2>
                    <p class="text-gray-600 mt-1">
                        <?php echo $action === 'add' ? 'Tambahkan testimoni baru dari pelanggan' : 'Perbarui informasi testimoni'; ?>
                    </p>
                </div>
                <a href="?" class="text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </a>
            </div>
        </div>

        <form method="POST" enctype="multipart/form-data" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Customer Name -->
                    <div>
                        <label for="nama_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Pelanggan <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="nama_pelanggan"
                               name="nama_pelanggan"
                               value="<?php echo htmlspecialchars($testimonial_data['nama_pelanggan'] ?? ''); ?>"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Contoh: Budi Santoso">
                    </div>

                    <!-- Testimonial Content -->
                    <div>
                        <label for="isi_testimoni" class="block text-sm font-medium text-gray-700 mb-2">
                            Isi Testimoni <span class="text-red-500">*</span>
                        </label>
                        <textarea id="isi_testimoni"
                                  name="isi_testimoni"
                                  rows="6"
                                  required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Tulis testimoni pelanggan di sini..."><?php echo htmlspecialchars($testimonial_data['isi_testimoni'] ?? ''); ?></textarea>
                        <p class="text-sm text-gray-500 mt-2">
                            Tulis testimoni dalam bentuk kutipan langsung dari pelanggan.
                        </p>
                    </div>

                    <!-- Rating -->
                    <div>
                        <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">
                            Rating <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center space-x-4">
                            <select id="rating"
                                    name="rating"
                                    required
                                    class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                <option value="">Pilih Rating</option>
                                <option value="5" <?php echo ($testimonial_data['rating'] ?? '') == '5' ? 'selected' : ''; ?>>⭐⭐⭐⭐⭐ (5 - Sangat Puas)</option>
                                <option value="4" <?php echo ($testimonial_data['rating'] ?? '') == '4' ? 'selected' : ''; ?>>⭐⭐⭐⭐ (4 - Puas)</option>
                                <option value="3" <?php echo ($testimonial_data['rating'] ?? '') == '3' ? 'selected' : ''; ?>>⭐⭐⭐ (3 - Cukup)</option>
                                <option value="2" <?php echo ($testimonial_data['rating'] ?? '') == '2' ? 'selected' : ''; ?>>⭐⭐ (2 - Kurang)</option>
                                <option value="1" <?php echo ($testimonial_data['rating'] ?? '') == '1' ? 'selected' : ''; ?>>⭐ (1 - Sangat Kurang)</option>
                            </select>

                            <!-- Rating Preview -->
                            <div id="rating-preview" class="flex items-center">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <svg class="w-6 h-6 rating-star <?php echo $i <= ($testimonial_data['rating'] ?? 0) ? 'text-yellow-400' : 'text-gray-300'; ?>"
                                         fill="currentColor" viewBox="0 0 20 20" data-rating="<?php echo $i; ?>">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <select id="status"
                                name="status"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="aktif" <?php echo ($testimonial_data['status'] ?? 'aktif') === 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="nonaktif" <?php echo ($testimonial_data['status'] ?? '') === 'nonaktif' ? 'selected' : ''; ?>>Non-aktif</option>
                        </select>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Customer Photo -->
                    <div>
                        <label for="foto_pelanggan" class="block text-sm font-medium text-gray-700 mb-2">
                            Foto Pelanggan
                        </label>

                        <!-- Current Photo Preview -->
                        <?php if ($action === 'edit' && $testimonial_data && $testimonial_data['foto_pelanggan']): ?>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 mb-2">Foto saat ini:</p>
                                <img src="<?php echo UPLOADS_URL; ?>/testimonials/<?php echo $testimonial_data['foto_pelanggan']; ?>"
                                     alt="Current photo"
                                     class="w-24 h-24 object-cover rounded-full border border-gray-200">
                            </div>
                        <?php endif; ?>

                        <input type="file"
                               id="foto_pelanggan"
                               name="foto_pelanggan"
                               accept="image/*"
                               data-preview="photo-preview"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">

                        <!-- Photo Preview -->
                        <img id="photo-preview"
                             class="mt-4 w-24 h-24 object-cover rounded-full border border-gray-200 hidden"
                             alt="Preview">

                        <p class="text-sm text-gray-500 mt-2">
                            Format: JPG, PNG, GIF. Maksimal 5MB. Foto akan dipotong menjadi lingkaran.
                        </p>
                    </div>

                    <!-- Testimonial Tips -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Tips Testimoni Berkualitas:</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Gunakan bahasa yang natural dan personal</li>
                            <li>• Sertakan detail spesifik tentang produk/layanan</li>
                            <li>• Hindari testimoni yang terlalu umum</li>
                            <li>• Pastikan testimoni mencerminkan pengalaman nyata</li>
                            <li>• Gunakan foto pelanggan yang jelas dan profesional</li>
                        </ul>
                    </div>

                    <!-- Preview Card -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Preview Testimoni:</h4>
                        <div id="testimonial-preview" class="bg-white rounded-lg p-4 border border-gray-200">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-500 font-semibold" id="preview-initial">?</span>
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900" id="preview-name">Nama Pelanggan</div>
                                    <div class="flex items-center" id="preview-rating">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                            <blockquote class="text-gray-600 italic" id="preview-content">
                                "Isi testimoni akan muncul di sini..."
                            </blockquote>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <a href="?" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <?php echo $action === 'add' ? 'Tambah Testimoni' : 'Perbarui Testimoni'; ?>
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php
$additional_js = "
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rating interaction
    const ratingSelect = document.getElementById('rating');
    const ratingStars = document.querySelectorAll('.rating-star');

    if (ratingSelect && ratingStars.length > 0) {
        ratingSelect.addEventListener('change', function() {
            updateRatingStars(this.value);
        });

        ratingStars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.dataset.rating;
                ratingSelect.value = rating;
                updateRatingStars(rating);
                updatePreview();
            });
        });
    }

    function updateRatingStars(rating) {
        ratingStars.forEach((star, index) => {
            if (index < rating) {
                star.classList.remove('text-gray-300');
                star.classList.add('text-yellow-400');
            } else {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            }
        });
    }

    // Live preview
    const nameInput = document.getElementById('nama_pelanggan');
    const contentInput = document.getElementById('isi_testimoni');

    if (nameInput) nameInput.addEventListener('input', updatePreview);
    if (contentInput) contentInput.addEventListener('input', updatePreview);
    if (ratingSelect) ratingSelect.addEventListener('change', updatePreview);

    function updatePreview() {
        const name = nameInput?.value || 'Nama Pelanggan';
        const content = contentInput?.value || 'Isi testimoni akan muncul di sini...';
        const rating = parseInt(ratingSelect?.value) || 0;

        // Update preview name
        const previewName = document.getElementById('preview-name');
        const previewInitial = document.getElementById('preview-initial');
        if (previewName) previewName.textContent = name;
        if (previewInitial) previewInitial.textContent = name.charAt(0).toUpperCase();

        // Update preview content
        const previewContent = document.getElementById('preview-content');
        if (previewContent) previewContent.textContent = '\"' + content + '\"';

        // Update preview rating
        const previewRating = document.getElementById('preview-rating');
        if (previewRating) {
            const stars = previewRating.querySelectorAll('svg');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.remove('text-gray-300');
                    star.classList.add('text-yellow-400');
                } else {
                    star.classList.remove('text-yellow-400');
                    star.classList.add('text-gray-300');
                }
            });
        }
    }

    // Initialize preview
    updatePreview();
});
</script>
";

require_once 'includes/footer.php';
?>
