<?php
$page_title = "Manajemen Produk";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500">Manajemen Produk</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        $nama_produk = sanitizeInput($_POST['nama_produk'] ?? '');
        $deskripsi = sanitizeInput($_POST['deskripsi'] ?? '');
        $harga = (float)($_POST['harga'] ?? 0);
        $status = sanitizeInput($_POST['status'] ?? 'aktif');
        
        // Validation
        $errors = [];
        if (empty($nama_produk)) $errors[] = 'Nama produk wajib diisi';
        if (empty($deskripsi)) $errors[] = 'Deskripsi wajib diisi';
        if ($harga <= 0) $errors[] = 'Harga harus lebih dari 0';
        
        if (empty($errors)) {
            $data = [
                'nama_produk' => $nama_produk,
                'deskripsi' => $deskripsi,
                'harga' => $harga,
                'status' => $status
            ];
            
            // Handle image upload
            if (isset($_FILES['gambar_produk']) && $_FILES['gambar_produk']['error'] === UPLOAD_ERR_OK) {
                $upload_result = uploadFile($_FILES['gambar_produk'], UPLOAD_PRODUCT_PATH);
                if ($upload_result['success']) {
                    $data['gambar_produk'] = $upload_result['filename'];
                } else {
                    $errors[] = $upload_result['message'];
                }
            }
            
            if (empty($errors)) {
                if ($action === 'add') {
                    if (insertData('produk', $data)) {
                        $success_message = 'Produk berhasil ditambahkan!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal menambahkan produk.';
                    }
                } elseif ($action === 'edit' && $id) {
                    // Delete old image if new one uploaded
                    if (isset($data['gambar_produk'])) {
                        $old_product = fetchSingle("SELECT gambar_produk FROM produk WHERE id_produk = ?", [$id]);
                        if ($old_product && $old_product['gambar_produk']) {
                            deleteFile(UPLOAD_PRODUCT_PATH . '/' . $old_product['gambar_produk']);
                        }
                    }
                    
                    if (updateData('produk', $data, 'id_produk = ?', [$id])) {
                        $success_message = 'Produk berhasil diperbarui!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal memperbarui produk.';
                    }
                }
            } else {
                $error_message = implode('<br>', $errors);
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    $product = fetchSingle("SELECT * FROM produk WHERE id_produk = ?", [$id]);
    if ($product) {
        // Delete image file
        if ($product['gambar_produk']) {
            deleteFile(UPLOAD_PRODUCT_PATH . '/' . $product['gambar_produk']);
        }
        
        if (deleteData('produk', 'id_produk = ?', [$id])) {
            $success_message = 'Produk berhasil dihapus!';
        } else {
            $error_message = 'Gagal menghapus produk.';
        }
    }
    $action = 'list';
}

// Get product data for edit
$product_data = null;
if ($action === 'edit' && $id) {
    $product_data = fetchSingle("SELECT * FROM produk WHERE id_produk = ?", [$id]);
    if (!$product_data) {
        $error_message = 'Produk tidak ditemukan.';
        $action = 'list';
    }
}

// Get products list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(nama_produk LIKE ? OR deskripsi LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM produk $where_clause";
    $total_products = fetchSingle($count_sql, $params)['total'];
    $total_pages = ceil($total_products / $limit);
    
    // Get products
    $sql = "SELECT * FROM produk $where_clause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
    $products = fetchAll($sql, $params);
}
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Products List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Daftar Produk</h2>
                    <p class="text-gray-600 mt-1">Kelola produk air yang tersedia</p>
                </div>
                <a href="?action=add" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Tambah Produk
                </a>
            </div>
        </div>
        
        <!-- Search -->
        <div class="p-6 border-b border-gray-100">
            <form method="GET" class="flex gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Cari produk..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Cari
                </button>
                <?php if (!empty($search)): ?>
                    <a href="?" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Reset
                    </a>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if ($products): ?>
                        <?php foreach ($products as $product): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                                            <?php if ($product['gambar_produk']): ?>
                                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo $product['gambar_produk']; ?>" 
                                                     alt="<?php echo htmlspecialchars($product['nama_produk']); ?>" 
                                                     class="w-full h-full object-cover rounded-lg">
                                            <?php else: ?>
                                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                </svg>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($product['nama_produk']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500 max-w-xs truncate">
                                                <?php echo htmlspecialchars($product['deskripsi']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo formatCurrency($product['harga']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $product['status'] === 'aktif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                        <?php echo ucfirst($product['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo formatDate($product['created_at']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <a href="?action=edit&id=<?php echo $product['id_produk']; ?>" 
                                       class="text-primary-600 hover:text-primary-900">Edit</a>
                                    <a href="?action=delete&id=<?php echo $product['id_produk']; ?>" 
                                       class="text-red-600 hover:text-red-900"
                                       data-confirm-delete="Yakin ingin menghapus produk '<?php echo htmlspecialchars($product['nama_produk']); ?>'?">Hapus</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <?php if (!empty($search)): ?>
                                    Tidak ada produk yang cocok dengan pencarian "<?php echo htmlspecialchars($search); ?>"
                                <?php else: ?>
                                    Belum ada produk. <a href="?action=add" class="text-primary-600 hover:text-primary-700">Tambah produk pertama</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 border-t border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Menampilkan <?php echo min($offset + 1, $total_products); ?>-<?php echo min($offset + $limit, $total_products); ?> 
                        dari <?php echo $total_products; ?> produk
                    </div>
                    <div class="flex space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>" 
                               class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>" 
                               class="px-3 py-2 border rounded-lg <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white border-gray-300 hover:bg-gray-50'; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>" 
                               class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo $action === 'add' ? 'Tambah Produk Baru' : 'Edit Produk'; ?>
                    </h2>
                    <p class="text-gray-600 mt-1">
                        <?php echo $action === 'add' ? 'Tambahkan produk air baru ke katalog' : 'Perbarui informasi produk'; ?>
                    </p>
                </div>
                <a href="?" class="text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </a>
            </div>
        </div>
        
        <form method="POST" enctype="multipart/form-data" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Product Name -->
                    <div>
                        <label for="nama_produk" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Produk <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="nama_produk" 
                               name="nama_produk" 
                               value="<?php echo htmlspecialchars($product_data['nama_produk'] ?? ''); ?>"
                               required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Contoh: Air Isi Ulang 19L">
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="deskripsi" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi <span class="text-red-500">*</span>
                        </label>
                        <textarea id="deskripsi" 
                                  name="deskripsi" 
                                  rows="4" 
                                  required 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Deskripsi detail produk..."><?php echo htmlspecialchars($product_data['deskripsi'] ?? ''); ?></textarea>
                    </div>
                    
                    <!-- Price -->
                    <div>
                        <label for="harga" class="block text-sm font-medium text-gray-700 mb-2">
                            Harga <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-3 text-gray-500">Rp</span>
                            <input type="number" 
                                   id="harga" 
                                   name="harga" 
                                   value="<?php echo $product_data['harga'] ?? ''; ?>"
                                   required 
                                   min="0"
                                   step="100"
                                   class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                   placeholder="0">
                        </div>
                    </div>
                    
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <select id="status" 
                                name="status" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="aktif" <?php echo ($product_data['status'] ?? 'aktif') === 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="nonaktif" <?php echo ($product_data['status'] ?? '') === 'nonaktif' ? 'selected' : ''; ?>>Non-aktif</option>
                        </select>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Product Image -->
                    <div>
                        <label for="gambar_produk" class="block text-sm font-medium text-gray-700 mb-2">
                            Gambar Produk
                        </label>
                        
                        <!-- Current Image Preview -->
                        <?php if ($action === 'edit' && $product_data && $product_data['gambar_produk']): ?>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 mb-2">Gambar saat ini:</p>
                                <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo $product_data['gambar_produk']; ?>" 
                                     alt="Current product image" 
                                     class="w-32 h-32 object-cover rounded-lg border border-gray-200">
                            </div>
                        <?php endif; ?>
                        
                        <input type="file" 
                               id="gambar_produk" 
                               name="gambar_produk" 
                               accept="image/*"
                               data-preview="image-preview"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        
                        <!-- Image Preview -->
                        <img id="image-preview" 
                             class="mt-4 w-32 h-32 object-cover rounded-lg border border-gray-200 hidden" 
                             alt="Preview">
                        
                        <p class="text-sm text-gray-500 mt-2">
                            Format: JPG, PNG, GIF. Maksimal 5MB.
                        </p>
                    </div>
                    
                    <!-- Upload Tips -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Tips Upload Gambar:</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Gunakan gambar dengan rasio 1:1 (persegi)</li>
                            <li>• Resolusi minimal 400x400 pixel</li>
                            <li>• Background putih atau transparan</li>
                            <li>• Fokus pada produk utama</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <a href="?" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <?php echo $action === 'add' ? 'Tambah Produk' : 'Perbarui Produk'; ?>
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
