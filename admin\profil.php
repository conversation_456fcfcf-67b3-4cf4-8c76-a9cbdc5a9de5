<?php
$page_title = "Manajemen Profil";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500">Manajemen Profil</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        $judul_bagian = sanitizeInput($_POST['judul_bagian'] ?? '');
        $isi_konten = sanitizeInput($_POST['isi_konten'] ?? '');
        $urutan = (int)($_POST['urutan'] ?? 1);
        $status = sanitizeInput($_POST['status'] ?? 'aktif');
        
        // Validation
        $errors = [];
        if (empty($judul_bagian)) $errors[] = 'Judul bagian wajib diisi';
        if (empty($isi_konten)) $errors[] = 'Isi konten wajib diisi';
        
        if (empty($errors)) {
            $data = [
                'judul_bagian' => $judul_bagian,
                'isi_konten' => $isi_konten,
                'urutan' => $urutan,
                'status' => $status
            ];
            
            if ($action === 'add') {
                if (insertData('profil', $data)) {
                    $success_message = 'Bagian profil berhasil ditambahkan!';
                    $action = 'list';
                } else {
                    $error_message = 'Gagal menambahkan bagian profil.';
                }
            } elseif ($action === 'edit' && $id) {
                if (updateData('profil', $data, 'id_profil = ?', [$id])) {
                    $success_message = 'Bagian profil berhasil diperbarui!';
                    $action = 'list';
                } else {
                    $error_message = 'Gagal memperbarui bagian profil.';
                }
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    if (deleteData('profil', 'id_profil = ?', [$id])) {
        $success_message = 'Bagian profil berhasil dihapus!';
    } else {
        $error_message = 'Gagal menghapus bagian profil.';
    }
    $action = 'list';
}

// Get profile data for edit
$profile_data = null;
if ($action === 'edit' && $id) {
    $profile_data = fetchSingle("SELECT * FROM profil WHERE id_profil = ?", [$id]);
    if (!$profile_data) {
        $error_message = 'Bagian profil tidak ditemukan.';
        $action = 'list';
    }
}

// Get profile sections list
if ($action === 'list') {
    $profiles = fetchAll("SELECT * FROM profil ORDER BY urutan ASC, created_at DESC");
}
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Profile Sections List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Konten Profil Usaha</h2>
                    <p class="text-gray-600 mt-1">Kelola konten halaman profil perusahaan</p>
                </div>
                <a href="?action=add" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Tambah Bagian
                </a>
            </div>
        </div>
        
        <!-- Content -->
        <div class="p-6">
            <?php if ($profiles): ?>
                <div class="space-y-4">
                    <?php foreach ($profiles as $profile): ?>
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-shadow">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <?php echo htmlspecialchars($profile['judul_bagian']); ?>
                                        </h3>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $profile['status'] === 'aktif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo ucfirst($profile['status']); ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                            Urutan: <?php echo $profile['urutan']; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="text-gray-600 leading-relaxed">
                                        <?php 
                                        $content = htmlspecialchars($profile['isi_konten']);
                                        echo strlen($content) > 300 ? substr($content, 0, 300) . '...' : $content;
                                        ?>
                                    </div>
                                    
                                    <div class="mt-4 text-sm text-gray-500">
                                        Terakhir diperbarui: <?php echo formatDate($profile['updated_at'] ?? $profile['created_at']); ?>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2 ml-4">
                                    <a href="?action=edit&id=<?php echo $profile['id_profil']; ?>" 
                                       class="text-primary-600 hover:text-primary-900 p-2 rounded-lg hover:bg-primary-50 transition-colors"
                                       data-tooltip="Edit">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <a href="?action=delete&id=<?php echo $profile['id_profil']; ?>" 
                                       class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                                       data-tooltip="Hapus"
                                       data-confirm-delete="Yakin ingin menghapus bagian '<?php echo htmlspecialchars($profile['judul_bagian']); ?>'?">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Konten Profil</h3>
                    <p class="text-gray-600 mb-6">Mulai dengan menambahkan bagian pertama untuk profil perusahaan.</p>
                    <a href="?action=add" class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Bagian Pertama
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Quick Templates -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-4">Template Bagian Profil</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="font-medium text-blue-900 mb-2">Sejarah Perusahaan</h4>
                <p class="text-sm text-blue-700 mb-3">Ceritakan awal mula dan perkembangan perusahaan</p>
                <a href="?action=add&template=sejarah" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Gunakan Template →
                </a>
            </div>
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="font-medium text-blue-900 mb-2">Visi & Misi</h4>
                <p class="text-sm text-blue-700 mb-3">Visi dan misi perusahaan untuk masa depan</p>
                <a href="?action=add&template=visi" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Gunakan Template →
                </a>
            </div>
            <div class="bg-white rounded-lg p-4 border border-blue-200">
                <h4 class="font-medium text-blue-900 mb-2">Nilai Perusahaan</h4>
                <p class="text-sm text-blue-700 mb-3">Nilai-nilai yang dipegang teguh perusahaan</p>
                <a href="?action=add&template=nilai" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Gunakan Template →
                </a>
            </div>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo $action === 'add' ? 'Tambah Bagian Profil' : 'Edit Bagian Profil'; ?>
                    </h2>
                    <p class="text-gray-600 mt-1">
                        <?php echo $action === 'add' ? 'Tambahkan konten baru untuk halaman profil' : 'Perbarui konten bagian profil'; ?>
                    </p>
                </div>
                <a href="?" class="text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </a>
            </div>
        </div>
        
        <form method="POST" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="space-y-6">
                <!-- Section Title -->
                <div>
                    <label for="judul_bagian" class="block text-sm font-medium text-gray-700 mb-2">
                        Judul Bagian <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="judul_bagian" 
                           name="judul_bagian" 
                           value="<?php echo htmlspecialchars($profile_data['judul_bagian'] ?? ($_GET['template'] === 'sejarah' ? 'Sejarah Perusahaan' : ($_GET['template'] === 'visi' ? 'Visi & Misi' : ($_GET['template'] === 'nilai' ? 'Nilai Perusahaan' : '')))); ?>"
                           required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                           placeholder="Contoh: Sejarah Perusahaan">
                </div>
                
                <!-- Content -->
                <div>
                    <label for="isi_konten" class="block text-sm font-medium text-gray-700 mb-2">
                        Isi Konten <span class="text-red-500">*</span>
                    </label>
                    <textarea id="isi_konten" 
                              name="isi_konten" 
                              rows="12" 
                              required 
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Tulis konten untuk bagian ini..."><?php 
                              echo htmlspecialchars($profile_data['isi_konten'] ?? 
                                  ($_GET['template'] === 'sejarah' ? 'Agen Air Fazza didirikan pada tahun [TAHUN] dengan komitmen menyediakan air bersih berkualitas tinggi untuk masyarakat. Dimulai dari usaha kecil keluarga, kini kami telah berkembang menjadi salah satu penyedia air terpercaya di daerah ini.\n\nDengan pengalaman bertahun-tahun dan dedikasi penuh, kami terus berinovasi dalam teknologi penyaringan air dan pelayanan pelanggan. Setiap tetes air yang kami distribusikan telah melalui proses penyaringan berlapis dan kontrol kualitas yang ketat.' : 
                                  ($_GET['template'] === 'visi' ? 'VISI:\nMenjadi penyedia air minum terpercaya yang mengutamakan kualitas, kebersihan, dan kepuasan pelanggan di seluruh Indonesia.\n\nMISI:\n1. Menyediakan air minum berkualitas tinggi dengan harga terjangkau\n2. Memberikan pelayanan terbaik kepada setiap pelanggan\n3. Berkontribusi pada kesehatan masyarakat melalui air bersih\n4. Mengembangkan teknologi penyaringan air yang ramah lingkungan\n5. Membangun kepercayaan jangka panjang dengan pelanggan' : 
                                  ($_GET['template'] === 'nilai' ? 'KUALITAS:\nKami berkomitmen memberikan air dengan kualitas terbaik melalui proses penyaringan berlapis dan kontrol kualitas yang ketat.\n\nKEPERCAYAAN:\nMembangun hubungan jangka panjang dengan pelanggan berdasarkan kepercayaan dan transparansi.\n\nPELAYANAN:\nMemberikan pelayanan yang ramah, cepat, dan responsif terhadap kebutuhan pelanggan.\n\nINOVASI:\nTerus berinovasi dalam teknologi dan metode pelayanan untuk memberikan yang terbaik.\n\nTANGGUNG JAWAB:\nBertanggung jawab terhadap kesehatan masyarakat dan kelestarian lingkungan.' : ''))); 
                              ?></textarea>
                    <p class="text-sm text-gray-500 mt-2">
                        Gunakan enter untuk membuat paragraf baru. Konten akan ditampilkan sesuai format yang Anda tulis.
                    </p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Order -->
                    <div>
                        <label for="urutan" class="block text-sm font-medium text-gray-700 mb-2">
                            Urutan Tampil
                        </label>
                        <input type="number" 
                               id="urutan" 
                               name="urutan" 
                               value="<?php echo $profile_data['urutan'] ?? 1; ?>"
                               min="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="1">
                        <p class="text-sm text-gray-500 mt-1">
                            Angka yang lebih kecil akan ditampilkan lebih dulu
                        </p>
                    </div>
                    
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <select id="status" 
                                name="status" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="aktif" <?php echo ($profile_data['status'] ?? 'aktif') === 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="nonaktif" <?php echo ($profile_data['status'] ?? '') === 'nonaktif' ? 'selected' : ''; ?>>Non-aktif</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <a href="?" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <?php echo $action === 'add' ? 'Tambah Bagian' : 'Perbarui Bagian'; ?>
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
