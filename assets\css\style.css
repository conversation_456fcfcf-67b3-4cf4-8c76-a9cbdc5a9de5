@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --primary-blue: #3B82F6;
  --primary-cyan: #06B6D4;
  --primary-teal: #14B8A6;
  --secondary-purple: #8B5CF6;
  --secondary-pink: #EC4899;
  --secondary-orange: #F59E0B;
  --gradient-primary: linear-gradient(135deg, #3B82F6, #06B6D4);
  --gradient-secondary: linear-gradient(135deg, #8B5CF6, #EC4899);
  --gradient-accent: linear-gradient(135deg, #14B8A6, #F59E0B);
}

/* Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-neutral-700 bg-neutral-50;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold text-neutral-900;
  }
  
  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }
  
  h5 {
    @apply text-lg md:text-xl lg:text-2xl;
  }
  
  h6 {
    @apply text-base md:text-lg lg:text-xl;
  }
}

/* Component Styles */
@layer components {
  /* Buttons */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 border-0;
  }
  
  .btn-secondary {
    @apply bg-white text-primary-600 border-2 border-primary-500 px-6 py-3 rounded-xl font-semibold hover:bg-primary-50 transition-all duration-300;
  }
  
  .btn-ghost {
    @apply text-primary-600 px-6 py-3 rounded-xl font-semibold hover:bg-primary-50 transition-all duration-300;
  }
  
  .btn-danger {
    @apply bg-red-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-600 transition-colors duration-200;
  }
  
  .btn-success {
    @apply bg-green-500 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-600 transition-colors duration-200;
  }
  
  /* Cards */
  .card-modern {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium p-6 transition-all duration-300 border border-neutral-100;
  }
  
  .card-gradient {
    @apply bg-gradient-to-br from-white to-neutral-50 rounded-2xl shadow-soft p-6 border border-neutral-100;
  }
  
  .card-floating {
    @apply bg-white rounded-2xl shadow-medium p-6 transform hover:-translate-y-2 hover:shadow-hard transition-all duration-300;
  }
  
  .card-product {
    @apply bg-white rounded-2xl shadow-soft hover:shadow-medium p-4 transition-all duration-300 border border-neutral-100 group;
  }
  
  /* Forms */
  .input-field {
    @apply w-full px-4 py-3 rounded-xl border border-neutral-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300 bg-white;
  }
  
  .textarea-field {
    @apply w-full px-4 py-3 rounded-xl border border-neutral-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 resize-none transition-all duration-300 bg-white;
  }
  
  .select-field {
    @apply w-full px-4 py-3 rounded-xl border border-neutral-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-300 bg-white;
  }
  
  /* Navigation */
  .nav-link {
    @apply text-neutral-600 hover:text-primary-600 font-medium transition-colors duration-200 relative;
  }
  
  .nav-link.active {
    @apply text-primary-600;
  }
  
  .nav-link::after {
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300;
    content: '';
  }
  
  .nav-link:hover::after,
  .nav-link.active::after {
    @apply w-full;
  }
  
  /* Hero Section */
  .hero-gradient {
    background: linear-gradient(135deg, #3B82F6 0%, #06B6D4 50%, #14B8A6 100%);
  }
  
  .hero-text-gradient {
    background: linear-gradient(135deg, #3B82F6, #06B6D4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Animations */
  .hover-lift {
    @apply transform hover:-translate-y-1 transition-transform duration-300;
  }
  
  .hover-scale {
    @apply transform hover:scale-105 transition-transform duration-300;
  }
  
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
  
  /* Loading */
  .loading-pulse {
    @apply animate-pulse-glow;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500;
  }
  
  /* Table */
  .table-modern {
    @apply w-full bg-white rounded-xl shadow-soft overflow-hidden;
  }
  
  .table-modern th {
    @apply bg-neutral-50 px-6 py-4 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider;
  }
  
  .table-modern td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-neutral-900 border-t border-neutral-100;
  }
  
  /* Badge */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }
  
  /* Sidebar */
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-neutral-600 hover:bg-primary-50 hover:text-primary-600 rounded-lg transition-colors duration-200;
  }
  
  .sidebar-link.active {
    @apply bg-primary-100 text-primary-700;
  }
  
  /* Modal */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  }
  
  .modal-content {
    @apply bg-white rounded-2xl shadow-hard max-w-md w-full mx-4 p-6 animate-scale-in;
  }
  
  /* Dropdown */
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-medium border border-neutral-100 py-1 z-10;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900;
  }
}

/* Utility Classes */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #3B82F6, #06B6D4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .bg-pattern {
    background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .mobile-menu {
    @apply fixed inset-0 bg-white z-50 p-4;
  }
  
  .mobile-menu-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }
}
