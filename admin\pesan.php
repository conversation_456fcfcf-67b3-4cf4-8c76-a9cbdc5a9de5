<?php
$page_title = "Pesan Masuk";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500"><PERSON>esan Masuk</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? '';

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'update_status') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        $status = sanitizeInput($_POST['status'] ?? '');
        $balasan = sanitizeInput($_POST['balasan'] ?? '');

        $data = ['status' => $status];
        if (!empty($balasan)) {
            $data['balasan'] = $balasan;
            $data['tanggal_balasan'] = date('Y-m-d H:i:s');
        }

        if (updateData('pesan_kontak', $data, 'id_pesan = ?', [$id])) {
            $success_message = 'Status pesan berhasil diperbarui!';
        } else {
            $error_message = 'Gagal memperbarui status pesan.';
        }
    }
    $action = 'list';
}

// Handle delete
if ($action === 'delete' && $id) {
    if (deleteData('pesan_kontak', 'id_pesan = ?', [$id])) {
        $success_message = 'Pesan berhasil dihapus!';
    } else {
        $error_message = 'Gagal menghapus pesan.';
    }
    $action = 'list';
}

// Get message data for view/reply
$message_data = null;
if (($action === 'view' || $action === 'reply') && $id) {
    $message_data = fetchSingle("SELECT * FROM pesan_kontak WHERE id_pesan = ?", [$id]);
    if (!$message_data) {
        $error_message = 'Pesan tidak ditemukan.';
        $action = 'list';
    } else {
        // Mark as read if status is 'baru'
        if ($message_data['status'] === 'baru') {
            updateData('pesan_kontak', ['status' => 'dibaca'], 'id_pesan = ?', [$id]);
            $message_data['status'] = 'dibaca';
        }
    }
}

// Get messages list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = 15;
    $offset = ($page - 1) * $limit;

    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(nama_pengirim LIKE ? OR email_pengirim LIKE ? OR subjek LIKE ? OR isi_pesan LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM pesan_kontak $where_clause";
    $total_messages = fetchSingle($count_sql, $params)['total'];
    $total_pages = ceil($total_messages / $limit);

    // Get messages
    $sql = "SELECT * FROM pesan_kontak $where_clause ORDER BY tanggal_kirim DESC LIMIT $limit OFFSET $offset";
    $messages = fetchAll($sql, $params);

    // Get status counts
    $status_counts = [
        'baru' => fetchSingle("SELECT COUNT(*) as total FROM pesan_kontak WHERE status = 'baru'")['total'],
        'dibaca' => fetchSingle("SELECT COUNT(*) as total FROM pesan_kontak WHERE status = 'dibaca'")['total'],
        'dibalas' => fetchSingle("SELECT COUNT(*) as total FROM pesan_kontak WHERE status = 'dibalas'")['total']
    ];
}
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Messages List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Pesan Masuk</h2>
                    <p class="text-gray-600 mt-1">Kelola pesan dari pengunjung website</p>
                </div>

                <!-- Status Summary -->
                <div class="flex space-x-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                        <span class="text-gray-600">Baru: <?php echo $status_counts['baru']; ?></span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-gray-600">Dibaca: <?php echo $status_counts['dibaca']; ?></span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-gray-600">Dibalas: <?php echo $status_counts['dibalas']; ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="p-6 border-b border-gray-100">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text"
                           name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Cari pesan..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="w-full md:w-48">
                    <select name="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Semua Status</option>
                        <option value="baru" <?php echo $status_filter === 'baru' ? 'selected' : ''; ?>>Baru</option>
                        <option value="dibaca" <?php echo $status_filter === 'dibaca' ? 'selected' : ''; ?>>Dibaca</option>
                        <option value="dibalas" <?php echo $status_filter === 'dibalas' ? 'selected' : ''; ?>>Dibalas</option>
                    </select>
                </div>
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Filter
                </button>
                <?php if (!empty($search) || !empty($status_filter)): ?>
                    <a href="?" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Reset
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Messages List -->
        <div class="divide-y divide-gray-100">
            <?php if ($messages): ?>
                <?php foreach ($messages as $message): ?>
                    <div class="p-6 hover:bg-gray-50 transition-colors <?php echo $message['status'] === 'baru' ? 'bg-blue-50' : ''; ?>">
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center space-x-3 mb-2">
                                    <!-- Status Badge -->
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        <?php
                                        echo match($message['status']) {
                                            'baru' => 'bg-red-100 text-red-800',
                                            'dibaca' => 'bg-blue-100 text-blue-800',
                                            'dibalas' => 'bg-green-100 text-green-800',
                                            default => 'bg-gray-100 text-gray-800'
                                        };
                                        ?>">
                                        <?php echo ucfirst($message['status']); ?>
                                    </span>

                                    <!-- Subject -->
                                    <h3 class="text-lg font-medium text-gray-900 truncate">
                                        <?php echo htmlspecialchars($message['subjek']); ?>
                                    </h3>
                                </div>

                                <!-- Sender Info -->
                                <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <?php echo htmlspecialchars($message['nama_pengirim']); ?>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <?php echo htmlspecialchars($message['email_pengirim']); ?>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <?php echo formatDate($message['tanggal_kirim']); ?>
                                    </div>
                                </div>

                                <!-- Message Preview -->
                                <p class="text-gray-600 line-clamp-2 leading-relaxed">
                                    <?php echo htmlspecialchars(substr($message['isi_pesan'], 0, 200)) . (strlen($message['isi_pesan']) > 200 ? '...' : ''); ?>
                                </p>

                                <!-- Reply Info -->
                                <?php if ($message['status'] === 'dibalas' && $message['tanggal_balasan']): ?>
                                    <div class="mt-3 text-sm text-green-600">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                        </svg>
                                        Dibalas pada <?php echo formatDate($message['tanggal_balasan']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center space-x-2 ml-4">
                                <a href="?action=view&id=<?php echo $message['id_pesan']; ?>"
                                   class="text-primary-600 hover:text-primary-900 p-2 rounded-lg hover:bg-primary-50 transition-colors"
                                   data-tooltip="Lihat Detail">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                <a href="?action=reply&id=<?php echo $message['id_pesan']; ?>"
                                   class="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50 transition-colors"
                                   data-tooltip="Balas">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                </a>
                                <a href="?action=delete&id=<?php echo $message['id_pesan']; ?>"
                                   class="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                                   data-tooltip="Hapus"
                                   data-confirm-delete="Yakin ingin menghapus pesan dari <?php echo htmlspecialchars($message['nama_pengirim']); ?>?">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="p-12 text-center">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak Ada Pesan</h3>
                    <p class="text-gray-600">
                        <?php if (!empty($search) || !empty($status_filter)): ?>
                            Tidak ada pesan yang cocok dengan filter yang dipilih.
                        <?php else: ?>
                            Belum ada pesan masuk dari pengunjung website.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 border-t border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Menampilkan <?php echo min($offset + 1, $total_messages); ?>-<?php echo min($offset + $limit, $total_messages); ?>
                        dari <?php echo $total_messages; ?> pesan
                    </div>
                    <div class="flex space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>"
                               class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>"
                               class="px-3 py-2 border rounded-lg <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white border-gray-300 hover:bg-gray-50'; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>"
                               class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

<?php elseif ($action === 'view' && $message_data): ?>
    <!-- Message Detail View -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Detail Pesan</h2>
                    <p class="text-gray-600 mt-1">Informasi lengkap pesan dari pengunjung</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="?action=reply&id=<?php echo $message_data['id_pesan']; ?>"
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors inline-flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                        </svg>
                        Balas Pesan
                    </a>
                    <a href="?" class="text-gray-600 hover:text-gray-900">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <!-- Message Content -->
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Message Details -->
                <div class="lg:col-span-2">
                    <!-- Subject -->
                    <div class="mb-6">
                        <h3 class="text-2xl font-semibold text-gray-900 mb-2">
                            <?php echo htmlspecialchars($message_data['subjek']); ?>
                        </h3>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                <?php
                                echo match($message_data['status']) {
                                    'baru' => 'bg-red-100 text-red-800',
                                    'dibaca' => 'bg-blue-100 text-blue-800',
                                    'dibalas' => 'bg-green-100 text-green-800',
                                    default => 'bg-gray-100 text-gray-800'
                                };
                                ?>">
                                <?php echo ucfirst($message_data['status']); ?>
                            </span>
                            <span><?php echo formatDate($message_data['tanggal_kirim']); ?></span>
                        </div>
                    </div>

                    <!-- Message Content -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Isi Pesan:</h4>
                        <div class="prose prose-gray max-w-none">
                            <?php echo nl2br(htmlspecialchars($message_data['isi_pesan'])); ?>
                        </div>
                    </div>

                    <!-- Reply Section -->
                    <?php if ($message_data['status'] === 'dibalas' && $message_data['balasan']): ?>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <h4 class="font-medium text-green-900 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                                Balasan Anda:
                            </h4>
                            <div class="prose prose-green max-w-none text-green-800">
                                <?php echo nl2br(htmlspecialchars($message_data['balasan'])); ?>
                            </div>
                            <div class="text-sm text-green-600 mt-3">
                                Dibalas pada: <?php echo formatDate($message_data['tanggal_balasan']); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sender Info -->
                <div class="lg:col-span-1">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="font-medium text-gray-900 mb-4">Informasi Pengirim</h4>

                        <div class="space-y-4">
                            <div>
                                <label class="text-sm font-medium text-gray-600">Nama:</label>
                                <p class="text-gray-900"><?php echo htmlspecialchars($message_data['nama_pengirim']); ?></p>
                            </div>

                            <div>
                                <label class="text-sm font-medium text-gray-600">Email:</label>
                                <p class="text-gray-900">
                                    <a href="mailto:<?php echo htmlspecialchars($message_data['email_pengirim']); ?>"
                                       class="text-primary-600 hover:text-primary-700">
                                        <?php echo htmlspecialchars($message_data['email_pengirim']); ?>
                                    </a>
                                </p>
                            </div>

                            <div>
                                <label class="text-sm font-medium text-gray-600">IP Address:</label>
                                <p class="text-gray-900 font-mono text-sm"><?php echo htmlspecialchars($message_data['ip_address']); ?></p>
                            </div>

                            <div>
                                <label class="text-sm font-medium text-gray-600">Tanggal Kirim:</label>
                                <p class="text-gray-900"><?php echo formatDate($message_data['tanggal_kirim']); ?></p>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h5 class="font-medium text-gray-900 mb-3">Aksi Cepat</h5>
                            <div class="space-y-2">
                                <a href="mailto:<?php echo htmlspecialchars($message_data['email_pengirim']); ?>?subject=Re: <?php echo urlencode($message_data['subjek']); ?>"
                                   class="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    Email Langsung
                                </a>
                                <a href="?action=reply&id=<?php echo $message_data['id_pesan']; ?>"
                                   class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors inline-flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                    Balas di Sistem
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($action === 'reply' && $message_data): ?>
    <!-- Reply Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Balas Pesan</h2>
                    <p class="text-gray-600 mt-1">Kirim balasan untuk pesan dari <?php echo htmlspecialchars($message_data['nama_pengirim']); ?></p>
                </div>
                <a href="?action=view&id=<?php echo $message_data['id_pesan']; ?>" class="text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Original Message -->
        <div class="p-6 border-b border-gray-100 bg-gray-50">
            <h3 class="font-medium text-gray-900 mb-3">Pesan Asli:</h3>
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-between mb-3">
                    <div class="text-sm text-gray-600">
                        <strong><?php echo htmlspecialchars($message_data['nama_pengirim']); ?></strong>
                        (<?php echo htmlspecialchars($message_data['email_pengirim']); ?>)
                    </div>
                    <div class="text-sm text-gray-500">
                        <?php echo formatDate($message_data['tanggal_kirim']); ?>
                    </div>
                </div>
                <div class="text-sm font-medium text-gray-900 mb-2">
                    <?php echo htmlspecialchars($message_data['subjek']); ?>
                </div>
                <div class="text-sm text-gray-600 line-clamp-3">
                    <?php echo nl2br(htmlspecialchars($message_data['isi_pesan'])); ?>
                </div>
            </div>
        </div>

        <!-- Reply Form -->
        <form method="POST" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            <input type="hidden" name="status" value="dibalas">

            <div class="space-y-6">
                <!-- Reply Message -->
                <div>
                    <label for="balasan" class="block text-sm font-medium text-gray-700 mb-2">
                        Balasan <span class="text-red-500">*</span>
                    </label>
                    <textarea id="balasan"
                              name="balasan"
                              rows="8"
                              required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Tulis balasan Anda di sini..."><?php echo htmlspecialchars($message_data['balasan'] ?? ''); ?></textarea>
                    <p class="text-sm text-gray-500 mt-2">
                        Balasan ini akan disimpan dalam sistem dan dapat dilihat di detail pesan.
                    </p>
                </div>

                <!-- Email Template -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Template Email:</h4>
                    <p class="text-sm text-blue-700 mb-3">
                        Anda juga dapat mengirim email langsung ke pengirim menggunakan template berikut:
                    </p>
                    <div class="bg-white rounded border border-blue-200 p-3 text-sm">
                        <div class="mb-2"><strong>To:</strong> <?php echo htmlspecialchars($message_data['email_pengirim']); ?></div>
                        <div class="mb-2"><strong>Subject:</strong> Re: <?php echo htmlspecialchars($message_data['subjek']); ?></div>
                        <div class="text-gray-600">
                            Halo <?php echo htmlspecialchars($message_data['nama_pengirim']); ?>,<br><br>
                            Terima kasih atas pesan Anda. [Tulis balasan di sini]<br><br>
                            Salam,<br>
                            Tim Agen Air Fazza
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="mailto:<?php echo htmlspecialchars($message_data['email_pengirim']); ?>?subject=Re: <?php echo urlencode($message_data['subjek']); ?>&body=<?php echo urlencode("Halo " . $message_data['nama_pengirim'] . ",\n\nTerima kasih atas pesan Anda.\n\n[Tulis balasan di sini]\n\nSalam,\nTim Agen Air Fazza"); ?>"
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Buka di Email Client →
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <a href="?action=view&id=<?php echo $message_data['id_pesan']; ?>"
                   class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Simpan Balasan
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>