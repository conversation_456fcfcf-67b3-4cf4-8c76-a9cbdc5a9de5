<?php
$page_title = "Manajemen Galeri";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500">Manajemen Galeri</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        $judul_gambar = sanitizeInput($_POST['judul_gambar'] ?? '');
        $deskripsi = sanitizeInput($_POST['deskripsi'] ?? '');
        $kategori = sanitizeInput($_POST['kategori'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'aktif');
        
        // Validation
        $errors = [];
        if (empty($judul_gambar)) $errors[] = 'Judul gambar wajib diisi';
        if (empty($kategori)) $errors[] = 'Kategori wajib diisi';
        
        if (empty($errors)) {
            $data = [
                'judul_gambar' => $judul_gambar,
                'deskripsi' => $deskripsi,
                'kategori' => $kategori,
                'status' => $status,
                'tanggal_upload' => date('Y-m-d H:i:s')
            ];
            
            // Handle image upload
            if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] === UPLOAD_ERR_OK) {
                $upload_result = uploadFile($_FILES['gambar'], UPLOAD_GALLERY_PATH);
                if ($upload_result['success']) {
                    $data['nama_file'] = $upload_result['filename'];
                } else {
                    $errors[] = $upload_result['message'];
                }
            } elseif ($action === 'add') {
                $errors[] = 'Gambar wajib diupload';
            }
            
            if (empty($errors)) {
                if ($action === 'add') {
                    if (insertData('galeri', $data)) {
                        $success_message = 'Foto berhasil ditambahkan ke galeri!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal menambahkan foto.';
                    }
                } elseif ($action === 'edit' && $id) {
                    // Delete old image if new one uploaded
                    if (isset($data['nama_file'])) {
                        $old_image = fetchSingle("SELECT nama_file FROM galeri WHERE id_galeri = ?", [$id]);
                        if ($old_image && $old_image['nama_file']) {
                            deleteFile(UPLOAD_GALLERY_PATH . '/' . $old_image['nama_file']);
                        }
                    } else {
                        unset($data['nama_file']); // Don't update filename if no new image
                    }
                    
                    if (updateData('galeri', $data, 'id_galeri = ?', [$id])) {
                        $success_message = 'Foto berhasil diperbarui!';
                        $action = 'list';
                    } else {
                        $error_message = 'Gagal memperbarui foto.';
                    }
                }
            } else {
                $error_message = implode('<br>', $errors);
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Handle delete
if ($action === 'delete' && $id) {
    $image = fetchSingle("SELECT * FROM galeri WHERE id_galeri = ?", [$id]);
    if ($image) {
        // Delete image file
        if ($image['nama_file']) {
            deleteFile(UPLOAD_GALLERY_PATH . '/' . $image['nama_file']);
        }
        
        if (deleteData('galeri', 'id_galeri = ?', [$id])) {
            $success_message = 'Foto berhasil dihapus dari galeri!';
        } else {
            $error_message = 'Gagal menghapus foto.';
        }
    }
    $action = 'list';
}

// Get image data for edit
$image_data = null;
if ($action === 'edit' && $id) {
    $image_data = fetchSingle("SELECT * FROM galeri WHERE id_galeri = ?", [$id]);
    if (!$image_data) {
        $error_message = 'Foto tidak ditemukan.';
        $action = 'list';
    }
}

// Get gallery images list
if ($action === 'list') {
    $search = $_GET['search'] ?? '';
    $kategori_filter = $_GET['kategori'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = 12;
    $offset = ($page - 1) * $limit;
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(judul_gambar LIKE ? OR deskripsi LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($kategori_filter)) {
        $where_conditions[] = "kategori = ?";
        $params[] = $kategori_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM galeri $where_clause";
    $total_images = fetchSingle($count_sql, $params)['total'];
    $total_pages = ceil($total_images / $limit);
    
    // Get images
    $sql = "SELECT * FROM galeri $where_clause ORDER BY tanggal_upload DESC LIMIT $limit OFFSET $offset";
    $images = fetchAll($sql, $params);
    
    // Get categories for filter
    $categories = fetchAll("SELECT DISTINCT kategori FROM galeri ORDER BY kategori");
}
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<?php if ($action === 'list'): ?>
    <!-- Gallery List -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <!-- Header -->
        <div class="p-6 border-b border-gray-100">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Galeri Foto</h2>
                    <p class="text-gray-600 mt-1">Kelola foto-foto kegiatan dan fasilitas</p>
                </div>
                <a href="?action=add" class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Upload Foto
                </a>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="p-6 border-b border-gray-100">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="Cari foto..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div class="w-full md:w-48">
                    <select name="kategori" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Semua Kategori</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat['kategori']); ?>" 
                                    <?php echo $kategori_filter === $cat['kategori'] ? 'selected' : ''; ?>>
                                <?php echo ucfirst($cat['kategori']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <button type="submit" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Filter
                </button>
                <?php if (!empty($search) || !empty($kategori_filter)): ?>
                    <a href="?" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Reset
                    </a>
                <?php endif; ?>
            </form>
        </div>
        
        <!-- Gallery Grid -->
        <div class="p-6">
            <?php if ($images): ?>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                    <?php foreach ($images as $image): ?>
                        <div class="group relative bg-gray-100 rounded-lg overflow-hidden aspect-square">
                            <img src="<?php echo UPLOADS_URL; ?>/gallery/<?php echo $image['nama_file']; ?>" 
                                 alt="<?php echo htmlspecialchars($image['judul_gambar']); ?>" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            
                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2">
                                    <a href="?action=edit&id=<?php echo $image['id_galeri']; ?>" 
                                       class="bg-white text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                                       data-tooltip="Edit">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </a>
                                    <a href="?action=delete&id=<?php echo $image['id_galeri']; ?>" 
                                       class="bg-red-500 text-white p-2 rounded-lg hover:bg-red-600 transition-colors"
                                       data-tooltip="Hapus"
                                       data-confirm-delete="Yakin ingin menghapus foto '<?php echo htmlspecialchars($image['judul_gambar']); ?>'?">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            
                            <!-- Status Badge -->
                            <div class="absolute top-2 left-2">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $image['status'] === 'aktif' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'; ?>">
                                    <?php echo ucfirst($image['status']); ?>
                                </span>
                            </div>
                            
                            <!-- Category Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="inline-flex px-2 py-1 text-xs font-medium bg-white bg-opacity-90 text-gray-700 rounded-full">
                                    <?php echo ucfirst($image['kategori']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Image Info -->
                        <div class="mt-2">
                            <h3 class="font-medium text-gray-900 truncate">
                                <?php echo htmlspecialchars($image['judul_gambar']); ?>
                            </h3>
                            <?php if ($image['deskripsi']): ?>
                                <p class="text-sm text-gray-600 truncate">
                                    <?php echo htmlspecialchars($image['deskripsi']); ?>
                                </p>
                            <?php endif; ?>
                            <p class="text-xs text-gray-500 mt-1">
                                <?php echo formatDate($image['tanggal_upload']); ?>
                            </p>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="flex items-center justify-between border-t border-gray-100 pt-6">
                        <div class="text-sm text-gray-700">
                            Menampilkan <?php echo min($offset + 1, $total_images); ?>-<?php echo min($offset + $limit, $total_images); ?> 
                            dari <?php echo $total_images; ?> foto
                        </div>
                        <div class="flex space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&kategori=<?php echo urlencode($kategori_filter); ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&kategori=<?php echo urlencode($kategori_filter); ?>" 
                                   class="px-3 py-2 border rounded-lg <?php echo $i === $page ? 'bg-primary-500 text-white border-primary-500' : 'bg-white border-gray-300 hover:bg-gray-50'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&kategori=<?php echo urlencode($kategori_filter); ?>" 
                                   class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Foto</h3>
                    <p class="text-gray-600 mb-6">
                        <?php if (!empty($search) || !empty($kategori_filter)): ?>
                            Tidak ada foto yang cocok dengan filter yang dipilih.
                        <?php else: ?>
                            Mulai dengan mengupload foto pertama ke galeri.
                        <?php endif; ?>
                    </p>
                    <?php if (empty($search) && empty($kategori_filter)): ?>
                        <a href="?action=add" class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Upload Foto Pertama
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">
                        <?php echo $action === 'add' ? 'Upload Foto Baru' : 'Edit Foto'; ?>
                    </h2>
                    <p class="text-gray-600 mt-1">
                        <?php echo $action === 'add' ? 'Tambahkan foto baru ke galeri' : 'Perbarui informasi foto'; ?>
                    </p>
                </div>
                <a href="?" class="text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </a>
            </div>
        </div>
        
        <form method="POST" enctype="multipart/form-data" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Image Title -->
                    <div>
                        <label for="judul_gambar" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Foto <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="judul_gambar" 
                               name="judul_gambar" 
                               value="<?php echo htmlspecialchars($image_data['judul_gambar'] ?? ''); ?>"
                               required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Contoh: Fasilitas Produksi">
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="deskripsi" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi
                        </label>
                        <textarea id="deskripsi" 
                                  name="deskripsi" 
                                  rows="4" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Deskripsi foto (opsional)..."><?php echo htmlspecialchars($image_data['deskripsi'] ?? ''); ?></textarea>
                    </div>
                    
                    <!-- Category -->
                    <div>
                        <label for="kategori" class="block text-sm font-medium text-gray-700 mb-2">
                            Kategori <span class="text-red-500">*</span>
                        </label>
                        <select id="kategori" 
                                name="kategori" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Pilih Kategori</option>
                            <option value="fasilitas" <?php echo ($image_data['kategori'] ?? '') === 'fasilitas' ? 'selected' : ''; ?>>Fasilitas</option>
                            <option value="produksi" <?php echo ($image_data['kategori'] ?? '') === 'produksi' ? 'selected' : ''; ?>>Proses Produksi</option>
                            <option value="kegiatan" <?php echo ($image_data['kategori'] ?? '') === 'kegiatan' ? 'selected' : ''; ?>>Kegiatan</option>
                            <option value="tim" <?php echo ($image_data['kategori'] ?? '') === 'tim' ? 'selected' : ''; ?>>Tim & Staff</option>
                            <option value="lainnya" <?php echo ($image_data['kategori'] ?? '') === 'lainnya' ? 'selected' : ''; ?>>Lainnya</option>
                        </select>
                    </div>
                    
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <select id="status" 
                                name="status" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="aktif" <?php echo ($image_data['status'] ?? 'aktif') === 'aktif' ? 'selected' : ''; ?>>Aktif</option>
                            <option value="nonaktif" <?php echo ($image_data['status'] ?? '') === 'nonaktif' ? 'selected' : ''; ?>>Non-aktif</option>
                        </select>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Image Upload -->
                    <div>
                        <label for="gambar" class="block text-sm font-medium text-gray-700 mb-2">
                            Foto <?php echo $action === 'add' ? '<span class="text-red-500">*</span>' : ''; ?>
                        </label>
                        
                        <!-- Current Image Preview -->
                        <?php if ($action === 'edit' && $image_data && $image_data['nama_file']): ?>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 mb-2">Foto saat ini:</p>
                                <img src="<?php echo UPLOADS_URL; ?>/gallery/<?php echo $image_data['nama_file']; ?>" 
                                     alt="Current image" 
                                     class="w-48 h-48 object-cover rounded-lg border border-gray-200">
                            </div>
                        <?php endif; ?>
                        
                        <input type="file" 
                               id="gambar" 
                               name="gambar" 
                               accept="image/*"
                               <?php echo $action === 'add' ? 'required' : ''; ?>
                               data-preview="image-preview"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        
                        <!-- Image Preview -->
                        <img id="image-preview" 
                             class="mt-4 w-48 h-48 object-cover rounded-lg border border-gray-200 hidden" 
                             alt="Preview">
                        
                        <p class="text-sm text-gray-500 mt-2">
                            Format: JPG, PNG, GIF. Maksimal 10MB. Rasio 1:1 (persegi) direkomendasikan.
                        </p>
                    </div>
                    
                    <!-- Upload Tips -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Tips Upload Foto:</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Gunakan foto dengan kualitas tinggi</li>
                            <li>• Pastikan pencahayaan yang baik</li>
                            <li>• Hindari foto yang blur atau gelap</li>
                            <li>• Pilih kategori yang sesuai</li>
                            <li>• Berikan judul yang deskriptif</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <a href="?" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    <?php echo $action === 'add' ? 'Upload Foto' : 'Perbarui Foto'; ?>
                </button>
            </div>
        </form>
    </div>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
