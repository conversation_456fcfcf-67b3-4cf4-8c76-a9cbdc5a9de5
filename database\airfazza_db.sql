-- Database: airfazza_db
-- Website Company Profile Agen Air Fazza
-- Created: 2025-07-08

CREATE DATABASE IF NOT EXISTS airfazza_db;
USE airfazza_db;

-- Tabel Admin untuk sistem login
CREATE TABLE admin (
    id_admin INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Produk untuk menampilkan produk air
CREATE TABLE produk (
    id_produk INT PRIMARY KEY AUTO_INCREMENT,
    nama_produk VARCHAR(100) NOT NULL,
    harga DECIMAL(10,2) NOT NULL,
    deskripsi TEXT,
    gambar_produk VARCHAR(255),
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Profil untuk konten halaman profil usaha
CREATE TABLE profil (
    id_profil INT PRIMARY KEY AUTO_INCREMENT,
    judul_bagian VARCHAR(100) NOT NULL,
    isi_konten TEXT NOT NULL,
    urutan INT DEFAULT 1,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Galeri untuk foto-foto kegiatan
CREATE TABLE galeri (
    id_gambar INT PRIMARY KEY AUTO_INCREMENT,
    judul_gambar VARCHAR(100) NOT NULL,
    nama_file VARCHAR(255) NOT NULL,
    deskripsi TEXT,
    kategori ENUM('kegiatan', 'produk', 'fasilitas', 'lainnya') DEFAULT 'kegiatan',
    tanggal_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif'
);

-- Tabel Pesan Kontak untuk form kontak
CREATE TABLE pesan_kontak (
    id_pesan INT PRIMARY KEY AUTO_INCREMENT,
    nama_pengirim VARCHAR(100) NOT NULL,
    email_pengirim VARCHAR(100) NOT NULL,
    subjek VARCHAR(200),
    isi_pesan TEXT NOT NULL,
    tanggal_kirim TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('baru', 'dibaca', 'dibalas') DEFAULT 'baru',
    ip_address VARCHAR(45)
);

-- Tabel Testimoni untuk ulasan pelanggan
CREATE TABLE testimoni (
    id_testimoni INT PRIMARY KEY AUTO_INCREMENT,
    nama_pelanggan VARCHAR(100) NOT NULL,
    foto_pelanggan VARCHAR(255),
    isi_testimoni TEXT NOT NULL,
    rating INT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5),
    status ENUM('aktif', 'nonaktif') DEFAULT 'aktif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel Pengaturan untuk konfigurasi website
CREATE TABLE pengaturan (
    id_pengaturan INT PRIMARY KEY AUTO_INCREMENT,
    nama_setting VARCHAR(100) NOT NULL UNIQUE,
    nilai_setting TEXT,
    deskripsi VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert data admin default
INSERT INTO admin (username, password, nama_lengkap, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>');
-- Password default: password (harus diubah setelah login pertama)

-- Insert data profil default
INSERT INTO profil (judul_bagian, isi_konten, urutan) VALUES 
('sejarah', 'Agen Air Fazza didirikan pada tahun 2020 dengan komitmen menyediakan air bersih berkualitas tinggi untuk masyarakat. Dimulai dari usaha kecil, kini kami telah melayani ribuan pelanggan di seluruh kota.', 1),
('visi', 'Menjadi penyedia air minum terpercaya yang mengutamakan kualitas, kebersihan, dan kepuasan pelanggan.', 2),
('misi', 'Menyediakan air minum berkualitas tinggi dengan harga terjangkau, memberikan pelayanan terbaik kepada pelanggan, dan berkontribusi pada kesehatan masyarakat.', 3);

-- Insert data produk sample
INSERT INTO produk (nama_produk, harga, deskripsi, gambar_produk) VALUES 
('Air Isi Ulang 19L', 5000.00, 'Air isi ulang dengan galon 19 liter, telah melalui proses penyaringan berlapis dan sterilisasi UV.', 'galon-19l.jpg'),
('Air Kemasan 600ml', 3000.00, 'Air mineral kemasan botol 600ml, praktis untuk dibawa kemana-mana.', 'botol-600ml.jpg'),
('Air Kemasan 1500ml', 5000.00, 'Air mineral kemasan botol 1.5 liter, cocok untuk keluarga.', 'botol-1500ml.jpg');

-- Insert pengaturan website
INSERT INTO pengaturan (nama_setting, nilai_setting, deskripsi) VALUES 
('nama_perusahaan', 'Agen Air Fazza', 'Nama perusahaan yang ditampilkan di website'),
('alamat', 'Jl. Contoh No. 123, Kota Contoh', 'Alamat lengkap perusahaan'),
('telepon', '0812-3456-7890', 'Nomor telepon perusahaan'),
('email', '<EMAIL>', 'Email perusahaan'),
('jam_operasional', 'Senin - Sabtu: 08.00 - 17.00 WIB', 'Jam operasional perusahaan'),
('maps_embed', '', 'Kode embed Google Maps'),
('hero_title', 'Air Bersih Berkualitas untuk Keluarga Sehat', 'Judul utama di hero section'),
('hero_subtitle', 'Kami menyediakan air isi ulang dan air kemasan dengan kualitas terjamin dan harga terjangkau', 'Subtitle di hero section');

-- Insert testimoni sample
INSERT INTO testimoni (nama_pelanggan, isi_testimoni, rating) VALUES
('Ibu Sari', 'Pelayanan sangat memuaskan, air selalu bersih dan segar. Sudah berlangganan 2 tahun!', 5),
('Pak Budi', 'Harga terjangkau dan kualitas terjamin. Recommended!', 5),
('Ibu Maya', 'Pengiriman selalu tepat waktu, air berkualitas baik.', 4);

-- ==========================================
-- PENGATURAN TABLE
-- ==========================================

CREATE TABLE pengaturan (
    id_pengaturan INT AUTO_INCREMENT PRIMARY KEY,
    nama_setting VARCHAR(100) NOT NULL UNIQUE,
    nilai_setting TEXT,
    deskripsi TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO pengaturan (nama_setting, nilai_setting, deskripsi) VALUES
('site_name', 'Agen Air Fazza', 'Nama website'),
('site_description', 'Penyedia air bersih berkualitas tinggi untuk kebutuhan sehari-hari', 'Deskripsi website'),
('alamat', 'Jl. Contoh No. 123, Kota Contoh, Provinsi Contoh 12345', 'Alamat perusahaan'),
('telepon', '0812-3456-7890', 'Nomor telepon'),
('email', '<EMAIL>', 'Email perusahaan'),
('jam_operasional', 'Senin - Sabtu: 08.00 - 17.00 WIB', 'Jam operasional'),
('maps_embed', '', 'Google Maps embed code');

-- Insert sample admin user (password: password)
INSERT INTO admin (username, password, nama_lengkap, email) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>');
