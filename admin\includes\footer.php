                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                    sidebarOverlay.classList.toggle('hidden');
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.add('-translate-x-full');
                    sidebarOverlay.classList.add('hidden');
                });
            }

            // Auto-hide alerts
            const alerts = document.querySelectorAll('.alert-auto-hide');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });

            // Form validation
            const forms = document.querySelectorAll('[data-validate]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('border-red-500');
                            field.classList.remove('border-gray-300');
                        } else {
                            field.classList.remove('border-red-500');
                            field.classList.add('border-gray-300');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('Mohon lengkapi semua field yang wajib diisi.');
                    }
                });
            });

            // File upload preview
            const fileInputs = document.querySelectorAll('input[type="file"][data-preview]');
            fileInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const previewId = this.dataset.preview;
                    const preview = document.getElementById(previewId);

                    if (preview && this.files && this.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            preview.src = e.target.result;
                            preview.classList.remove('hidden');
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            });

            // Confirm delete
            const deleteLinks = document.querySelectorAll('[data-confirm-delete]');
            deleteLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const message = this.dataset.confirmDelete;
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                });
            });

            // Tooltips
            const tooltipElements = document.querySelectorAll('[data-tooltip]');
            tooltipElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg';
                    tooltip.textContent = this.dataset.tooltip;
                    tooltip.style.top = '-30px';
                    tooltip.style.left = '50%';
                    tooltip.style.transform = 'translateX(-50%)';

                    this.style.position = 'relative';
                    this.appendChild(tooltip);
                });

                element.addEventListener('mouseleave', function() {
                    const tooltip = this.querySelector('.absolute.z-50');
                    if (tooltip) {
                        tooltip.remove();
                    }
                });
            });
        });
    </script>

    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
</body>
</html>
