<?php
$page_title = "Dashboard";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>';

require_once 'includes/header.php';

// Get statistics
$stats = [
    'total_products' => fetchSingle("SELECT COUNT(*) as total FROM produk WHERE status = 'aktif'")['total'],
    'total_messages' => fetchSingle("SELECT COUNT(*) as total FROM pesan_kontak")['total'],
    'unread_messages' => fetchSingle("SELECT COUNT(*) as total FROM pesan_kontak WHERE status = 'baru'")['total'],
    'total_gallery' => fetchSingle("SELECT COUNT(*) as total FROM galeri WHERE status = 'aktif'")['total'],
    'total_testimonials' => fetchSingle("SELECT COUNT(*) as total FROM testimoni WHERE status = 'aktif'")['total']
];

// Get recent messages
$recent_messages = fetchAll("SELECT * FROM pesan_kontak ORDER BY tanggal_kirim DESC LIMIT 5");

// Get recent products
$recent_products = fetchAll("SELECT * FROM produk WHERE status = 'aktif' ORDER BY created_at DESC LIMIT 5");

// Get monthly message statistics for chart
$monthly_stats = fetchAll("
    SELECT 
        DATE_FORMAT(tanggal_kirim, '%Y-%m') as month,
        COUNT(*) as total
    FROM pesan_kontak 
    WHERE tanggal_kirim >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(tanggal_kirim, '%Y-%m')
    ORDER BY month ASC
");
?>

<!-- Welcome Section -->
<div class="mb-8">
    <div class="bg-gradient-to-r from-primary-600 to-blue-600 rounded-2xl p-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-heading font-bold mb-2">
                    Selamat Datang, <?php echo htmlspecialchars($_SESSION['admin_name']); ?>!
                </h2>
                <p class="text-blue-100 text-lg">
                    Kelola website Agen Air Fazza dengan mudah melalui dashboard admin ini.
                </p>
            </div>
            <div class="hidden md:block">
                <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Products -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Produk</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_products']; ?></p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
        </div>
        <div class="mt-4">
            <a href="produk.php" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                Kelola Produk →
            </a>
        </div>
    </div>
    
    <!-- Total Messages -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total Pesan</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_messages']; ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
            </div>
        </div>
        <div class="mt-4 flex items-center justify-between">
            <a href="pesan.php" class="text-sm text-green-600 hover:text-green-700 font-medium">
                Lihat Pesan →
            </a>
            <?php if ($stats['unread_messages'] > 0): ?>
                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                    <?php echo $stats['unread_messages']; ?> baru
                </span>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Total Gallery -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Foto Galeri</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_gallery']; ?></p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
        </div>
        <div class="mt-4">
            <a href="galeri.php" class="text-sm text-purple-600 hover:text-purple-700 font-medium">
                Kelola Galeri →
            </a>
        </div>
    </div>
    
    <!-- Total Testimonials -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Testimoni</p>
                <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_testimonials']; ?></p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
            </div>
        </div>
        <div class="mt-4">
            <a href="testimoni.php" class="text-sm text-orange-600 hover:text-orange-700 font-medium">
                Kelola Testimoni →
            </a>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Message Statistics Chart -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistik Pesan (6 Bulan Terakhir)</h3>
        <div class="h-64">
            <canvas id="messageChart"></canvas>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Cepat</h3>
        <div class="space-y-3">
            <a href="produk.php?action=add" class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Tambah Produk Baru</p>
                    <p class="text-sm text-gray-600">Tambahkan produk air baru</p>
                </div>
            </a>
            
            <a href="galeri.php?action=add" class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Upload Foto Galeri</p>
                    <p class="text-sm text-gray-600">Tambahkan foto kegiatan</p>
                </div>
            </a>
            
            <a href="pesan.php" class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Cek Pesan Masuk</p>
                    <p class="text-sm text-gray-600">
                        <?php echo $stats['unread_messages']; ?> pesan belum dibaca
                    </p>
                </div>
            </a>
            
            <a href="pengaturan.php" class="flex items-center p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="font-medium text-gray-900">Pengaturan Website</p>
                    <p class="text-sm text-gray-600">Kelola konfigurasi website</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Recent Messages -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Pesan Terbaru</h3>
                <a href="pesan.php" class="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    Lihat Semua
                </a>
            </div>
        </div>
        <div class="p-6">
            <?php if ($recent_messages): ?>
                <div class="space-y-4">
                    <?php foreach ($recent_messages as $message): ?>
                        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-primary-600 font-semibold text-sm">
                                    <?php echo strtoupper(substr($message['nama_pengirim'], 0, 1)); ?>
                                </span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        <?php echo htmlspecialchars($message['nama_pengirim']); ?>
                                    </p>
                                    <span class="text-xs text-gray-500">
                                        <?php echo timeAgo($message['tanggal_kirim']); ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 truncate">
                                    <?php echo htmlspecialchars($message['subjek']); ?>
                                </p>
                                <?php if ($message['status'] === 'baru'): ?>
                                    <span class="inline-block mt-1 bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                                        Baru
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <p class="text-gray-500">Belum ada pesan masuk</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Recent Products -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Produk Terbaru</h3>
                <a href="produk.php" class="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    Lihat Semua
                </a>
            </div>
        </div>
        <div class="p-6">
            <?php if ($recent_products): ?>
                <div class="space-y-4">
                    <?php foreach ($recent_products as $product): ?>
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                                <?php if ($product['gambar_produk']): ?>
                                    <img src="<?php echo UPLOADS_URL; ?>/products/<?php echo $product['gambar_produk']; ?>" 
                                         alt="<?php echo htmlspecialchars($product['nama_produk']); ?>" 
                                         class="w-full h-full object-cover rounded-lg">
                                <?php else: ?>
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    <?php echo htmlspecialchars($product['nama_produk']); ?>
                                </p>
                                <p class="text-sm text-gray-600">
                                    <?php echo formatCurrency($product['harga']); ?>
                                </p>
                                <p class="text-xs text-gray-500">
                                    <?php echo timeAgo($product['created_at']); ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <p class="text-gray-500">Belum ada produk</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Message Chart
    const ctx = document.getElementById('messageChart').getContext('2d');
    const monthlyData = " . json_encode($monthly_stats) . ";
    
    const labels = monthlyData.map(item => {
        const date = new Date(item.month + '-01');
        return date.toLocaleDateString('id-ID', { month: 'short', year: 'numeric' });
    });
    
    const data = monthlyData.map(item => item.total);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Jumlah Pesan',
                data: data,
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
});
</script>
";

require_once 'includes/footer.php';
?>
