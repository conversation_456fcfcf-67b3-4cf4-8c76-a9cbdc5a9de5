# Design System - Agen Air Fazza
## Gen-Z Aesthetic Approach

### Color Palette
```css
/* Primary Colors */
--primary-blue: #3B82F6;      /* Modern blue */
--primary-cyan: #06B6D4;      /* Fresh cyan */
--primary-teal: #14B8A6;      /* Clean teal */

/* Secondary Colors */
--secondary-purple: #8B5CF6;   /* Vibrant purple */
--secondary-pink: #EC4899;     /* Energetic pink */
--secondary-orange: #F59E0B;   /* Warm orange */

/* Neutral Colors */
--neutral-900: #111827;        /* Dark text */
--neutral-700: #374151;        /* Medium text */
--neutral-500: #6B7280;        /* Light text */
--neutral-200: #E5E7EB;        /* Light border */
--neutral-100: #F3F4F6;        /* Light background */
--neutral-50: #F9FAFB;         /* Lightest background */

/* Gradient Combinations */
--gradient-primary: linear-gradient(135deg, #3B82F6, #06B6D4);
--gradient-secondary: linear-gradient(135deg, #8B5CF6, #EC4899);
--gradient-accent: linear-gradient(135deg, #14B8A6, #F59E0B);
```

### Typography
```css
/* Font Families */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-heading: 'Poppins', sans-serif;
--font-mono: 'JetBrains Mono', monospace;

/* Font Sizes (Fluid Typography) */
--text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
--text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
--text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
--text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
--text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
--text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
--text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
--text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
```

### Spacing System
```css
/* Spacing Scale */
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
--space-20: 5rem;      /* 80px */
--space-24: 6rem;      /* 96px */
```

### Responsive Breakpoints
```css
/* Mobile First Approach */
--breakpoint-sm: 640px;    /* Small devices */
--breakpoint-md: 768px;    /* Medium devices */
--breakpoint-lg: 1024px;   /* Large devices */
--breakpoint-xl: 1280px;   /* Extra large devices */
--breakpoint-2xl: 1536px;  /* 2X large devices */
```

### Component Styles

#### Buttons
```css
/* Primary Button */
.btn-primary {
  @apply bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300;
}

/* Secondary Button */
.btn-secondary {
  @apply bg-white text-blue-600 border-2 border-blue-500 px-6 py-3 rounded-xl font-semibold hover:bg-blue-50 transition-all duration-300;
}

/* Ghost Button */
.btn-ghost {
  @apply text-blue-600 px-6 py-3 rounded-xl font-semibold hover:bg-blue-50 transition-all duration-300;
}
```

#### Cards
```css
/* Modern Card */
.card-modern {
  @apply bg-white rounded-2xl shadow-lg hover:shadow-xl p-6 transition-all duration-300 border border-gray-100;
}

/* Gradient Card */
.card-gradient {
  @apply bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg p-6 border border-gray-100;
}

/* Floating Card */
.card-floating {
  @apply bg-white rounded-2xl shadow-2xl p-6 transform hover:-translate-y-2 transition-all duration-300;
}
```

#### Forms
```css
/* Input Field */
.input-field {
  @apply w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300;
}

/* Textarea */
.textarea-field {
  @apply w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none transition-all duration-300;
}
```

### Animation & Transitions
```css
/* Hover Effects */
.hover-lift {
  @apply transform hover:-translate-y-1 transition-transform duration-300;
}

.hover-scale {
  @apply transform hover:scale-105 transition-transform duration-300;
}

/* Loading Animation */
@keyframes pulse-glow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-pulse {
  animation: pulse-glow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
```

### Layout Principles

#### Grid System
- 12-column grid system
- Flexible gutters: 1.5rem mobile, 2.5rem desktop
- Overlapping elements for dynamic feel
- Asymmetrical layouts for visual interest

#### Negative Space
- Generous white space between sections
- Breathing room around text elements
- Strategic use of empty space for focus

#### Visual Hierarchy
- Bold headings with gradient text
- Clear typography scale
- Color contrast for accessibility
- Strategic use of shadows and depth

### Accessibility Guidelines
- Minimum contrast ratio: 4.5:1 for normal text
- Focus indicators on all interactive elements
- Semantic HTML structure
- Alt text for all images
- Keyboard navigation support

### Mobile-First Approach
- Start with mobile design
- Progressive enhancement for larger screens
- Touch-friendly button sizes (minimum 44px)
- Optimized images for different screen densities
