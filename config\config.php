<?php
/**
 * General Configuration
 * Website Agen Air Fazza
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Jakarta');

// Site Configuration
define('SITE_NAME', 'Agen Air Fazza');
define('SITE_URL', 'http://localhost/sim-airfazza');
define('ADMIN_URL', SITE_URL . '/admin');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('INCLUDES_PATH', ROOT_PATH . '/includes');

// URL paths
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Upload Configuration
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_PRODUCT_PATH', UPLOADS_PATH . '/products');
define('UPLOAD_GALLERY_PATH', UPLOADS_PATH . '/gallery');
define('UPLOAD_TESTIMONIAL_PATH', UPLOADS_PATH . '/testimonials');

// Backup path
define('BACKUP_PATH', ROOT_PATH . '/backups');

// Create upload directories if they don't exist
$uploadDirs = [
    UPLOADS_PATH,
    UPLOAD_PRODUCT_PATH,
    UPLOAD_GALLERY_PATH,
    UPLOAD_TESTIMONIAL_PATH
];

foreach ($uploadDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Security Configuration
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Pagination
define('ITEMS_PER_PAGE', 10);

// Include database configuration
require_once 'database.php';

/**
 * Generate CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Check if user is logged in as admin
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_username']);
}

/**
 * Redirect to login if not authenticated
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: ' . ADMIN_URL . '/login.php');
        exit();
    }
}

/**
 * Logout admin
 */
function adminLogout() {
    session_unset();
    session_destroy();
    header('Location: ' . ADMIN_URL . '/login.php');
    exit();
}

/**
 * Format currency (Indonesian Rupiah)
 */
function formatCurrency($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

/**
 * Format date (Indonesian format)
 */
function formatDate($date, $format = 'd F Y') {
    $months = [
        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return $day . ' ' . $month . ' ' . $year;
}

/**
 * Format time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'baru saja';
    if ($time < 3600) return floor($time/60) . ' menit yang lalu';
    if ($time < 86400) return floor($time/3600) . ' jam yang lalu';
    if ($time < 2592000) return floor($time/86400) . ' hari yang lalu';
    if ($time < 31536000) return floor($time/2592000) . ' bulan yang lalu';
    
    return floor($time/31536000) . ' tahun yang lalu';
}

/**
 * Upload file function
 */
function uploadFile($file, $uploadPath, $allowedTypes = ALLOWED_IMAGE_TYPES) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }
    
    $fileName = $file['name'];
    $fileTmpName = $file['tmp_name'];
    $fileSize = $file['size'];
    $fileError = $file['error'];
    
    // Check for upload errors
    if ($fileError !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Upload error occurred'];
    }
    
    // Check file size
    if ($fileSize > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File size too large'];
    }
    
    // Get file extension
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    // Check allowed file types
    if (!in_array($fileExt, $allowedTypes)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    // Generate unique filename
    $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
    $destination = $uploadPath . '/' . $newFileName;
    
    // Move uploaded file
    if (move_uploaded_file($fileTmpName, $destination)) {
        return ['success' => true, 'filename' => $newFileName];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

/**
 * Delete file function
 */
function deleteFile($filePath) {
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * Get site settings
 */
function getSiteSetting($name, $default = '') {
    $setting = fetchSingle("SELECT nilai_setting FROM pengaturan WHERE nama_setting = ?", [$name]);
    return $setting ? $setting['nilai_setting'] : $default;
}

/**
 * Update site setting
 */
function updateSiteSetting($name, $value) {
    $existing = fetchSingle("SELECT id_pengaturan FROM pengaturan WHERE nama_setting = ?", [$name]);
    
    if ($existing) {
        return updateData('pengaturan', ['nilai_setting' => $value], 'nama_setting = ?', [$name]);
    } else {
        return insertData('pengaturan', ['nama_setting' => $name, 'nilai_setting' => $value]);
    }
}

/**
 * Generate pagination
 */
function generatePagination($currentPage, $totalPages, $baseUrl) {
    $pagination = '';
    
    if ($totalPages > 1) {
        $pagination .= '<nav class="flex justify-center mt-8">';
        $pagination .= '<div class="flex space-x-2">';
        
        // Previous button
        if ($currentPage > 1) {
            $pagination .= '<a href="' . $baseUrl . '?page=' . ($currentPage - 1) . '" class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Previous</a>';
        }
        
        // Page numbers
        for ($i = 1; $i <= $totalPages; $i++) {
            $active = ($i == $currentPage) ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50';
            $pagination .= '<a href="' . $baseUrl . '?page=' . $i . '" class="px-3 py-2 border border-gray-300 rounded-lg ' . $active . '">' . $i . '</a>';
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $pagination .= '<a href="' . $baseUrl . '?page=' . ($currentPage + 1) . '" class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">Next</a>';
        }
        
        $pagination .= '</div>';
        $pagination .= '</nav>';
    }
    
    return $pagination;
}

// ==========================================
// BACKUP & RESTORE FUNCTIONS
// ==========================================

/**
 * Create database backup
 */
function createDatabaseBackup() {
    try {
        // Create backup directory if not exists
        if (!file_exists(BACKUP_PATH)) {
            mkdir(BACKUP_PATH, 0755, true);
        }

        // Generate filename
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = BACKUP_PATH . '/' . $filename;

        // Get database connection details
        $host = DB_HOST;
        $username = DB_USER;
        $password = DB_PASS;
        $database = DB_NAME;

        // Create mysqldump command
        $command = "mysqldump --host={$host} --user={$username} --password={$password} {$database} > {$filepath}";

        // Execute command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        if ($return_var === 0 && file_exists($filepath)) {
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath
            ];
        } else {
            // Fallback: PHP-based backup
            return createPHPBackup($filepath, $filename);
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Create backup using PHP (fallback method)
 */
function createPHPBackup($filepath, $filename) {
    try {
        global $pdo;

        $sql_content = "-- Database Backup\n";
        $sql_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql_content .= "-- Database: " . DB_NAME . "\n\n";

        $sql_content .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";

        // Get all tables
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        foreach ($tables as $table) {
            // Get table structure
            $create_table = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch();
            $sql_content .= "-- Table structure for `{$table}`\n";
            $sql_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
            $sql_content .= $create_table['Create Table'] . ";\n\n";

            // Get table data
            $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
            if (!empty($rows)) {
                $sql_content .= "-- Data for table `{$table}`\n";

                foreach ($rows as $row) {
                    $values = array_map(function($value) use ($pdo) {
                        return $value === null ? 'NULL' : $pdo->quote($value);
                    }, array_values($row));

                    $sql_content .= "INSERT INTO `{$table}` VALUES (" . implode(', ', $values) . ");\n";
                }
                $sql_content .= "\n";
            }
        }

        $sql_content .= "SET FOREIGN_KEY_CHECKS = 1;\n";

        // Write to file
        if (file_put_contents($filepath, $sql_content)) {
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Gagal menulis file backup'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Restore database from backup
 */
function restoreDatabaseBackup($file) {
    try {
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'Error uploading file'];
        }

        if (pathinfo($file['name'], PATHINFO_EXTENSION) !== 'sql') {
            return ['success' => false, 'message' => 'File harus berformat SQL'];
        }

        // Read SQL content
        $sql_content = file_get_contents($file['tmp_name']);
        if ($sql_content === false) {
            return ['success' => false, 'message' => 'Gagal membaca file SQL'];
        }

        // Create automatic backup before restore
        $backup_result = createDatabaseBackup();
        if (!$backup_result['success']) {
            return ['success' => false, 'message' => 'Gagal membuat backup otomatis'];
        }

        global $pdo;

        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

        // Split SQL into individual statements
        $statements = preg_split('/;\s*$/m', $sql_content);

        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $pdo->exec($statement);
            }
        }

        // Re-enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

        return ['success' => true, 'message' => 'Database berhasil direstore'];

    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get list of backup files
 */
function getBackupFiles() {
    $files = [];

    if (!file_exists(BACKUP_PATH)) {
        return $files;
    }

    $backup_files = glob(BACKUP_PATH . '/*.sql');

    foreach ($backup_files as $file) {
        $files[] = [
            'name' => basename($file),
            'size' => filesize($file),
            'date' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }

    // Sort by date (newest first)
    usort($files, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });

    return $files;
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= (1 << (10 * $pow));

    return round($bytes, 2) . ' ' . $units[$pow];
}


?>
