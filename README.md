# 🌊 Sistem Informasi Agen Air Fazza

Website company profile modern untuk Agen Air Fazza dengan sistem manajemen konten lengkap.

## 🚀 Fitur Utama

### 🌐 Website Landing Page
- **5 Halaman Responsif**: Beranda, Produk, Profil, Galeri, Kontak
- **Modern UI/UX**: Design Gen-Z aesthetic dengan Tailwind CSS
- **Mobile-First**: Responsive design untuk semua devices
- **SEO-Friendly**: Struktur HTML yang optimal untuk search engine
- **Interactive Elements**: Smooth animations dan hover effects

### 🔧 Admin Dashboard
- **Complete CRUD System**: Manajemen semua konten website
- **Modern Interface**: Dark sidebar dengan light content area
- **Real-time Statistics**: Dashboard dengan charts dan analytics
- **File Management**: Upload dan preview gambar
- **Security Features**: CSRF protection dan authentication

## 📋 Modul Admin Dashboard

### 1. 🛍️ Manajemen Produk
- ✅ List produk dengan search & pagination
- ✅ Add/Edit form dengan upload gambar
- ✅ Delete dengan konfirmasi
- ✅ Status management (aktif/nonaktif)
- ✅ Image preview & validation

### 2. 🏢 Manajemen Profil
- ✅ Content management untuk halaman profil
- ✅ Template system (Sejarah, Visi/Misi, Nilai)
- ✅ Order management untuk urutan tampil
- ✅ Rich text content editing

### 3. 🖼️ Manajemen Galeri
- ✅ Grid layout dengan preview foto
- ✅ Category system (fasilitas, produksi, kegiatan, tim, lainnya)
- ✅ Image upload dengan preview
- ✅ Filter by category & search
- ✅ Hover overlay dengan action buttons

### 4. 📧 Manajemen Pesan
- ✅ List pesan dengan status badges
- ✅ Detail view pesan lengkap
- ✅ Reply system dengan template email
- ✅ Status management (baru, dibaca, dibalas)
- ✅ Search & filter by status
- ✅ Auto-mark as read

### 5. ⭐ Manajemen Testimoni
- ✅ Grid layout dengan rating display
- ✅ Interactive rating system (1-5 stars)
- ✅ Live preview testimoni
- ✅ Photo upload untuk pelanggan
- ✅ Rating statistics & average
- ✅ Filter by rating & search

### 6. ⚙️ Pengaturan Website
- ✅ General settings (nama, deskripsi, kontak)
- ✅ Database backup & restore
- ✅ File management
- ✅ System configuration

## 🛠️ Teknologi

- **Backend**: PHP Native dengan struktur MVC
- **Database**: MySQL dengan prepared statements
- **Frontend**: HTML5, CSS3, JavaScript ES6
- **Styling**: Tailwind CSS
- **Charts**: Chart.js
- **Icons**: Heroicons
- **Fonts**: Inter & Poppins (Google Fonts)

## 📦 Instalasi

### 1. Requirements
- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Apache/Nginx web server
- Extension PHP: PDO, GD, mbstring

### 2. Setup Database
```sql
-- Import database
mysql -u username -p database_name < database/airfazza_db.sql
```

### 3. Konfigurasi
```php
// Edit config/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'airfazza_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. Permissions
```bash
# Set permissions untuk upload directories
chmod 755 uploads/
chmod 755 uploads/products/
chmod 755 uploads/gallery/
chmod 755 uploads/testimonials/
chmod 755 backups/
```

### 5. Testing
Akses `test_system.php` untuk memverifikasi instalasi.

## 🔐 Login Admin

- **URL**: `/admin/login.php`
- **Username**: `admin`
- **Password**: `password`

⚠️ **Penting**: Ganti password default setelah login pertama!

## 📁 Struktur Direktori

```
sim-airfazza/
├── admin/                  # Admin dashboard
│   ├── includes/          # Header & footer admin
│   ├── index.php          # Dashboard utama
│   ├── produk.php         # CRUD produk
│   ├── profil.php         # CRUD profil
│   ├── galeri.php         # CRUD galeri
│   ├── pesan.php          # Manajemen pesan
│   ├── testimoni.php      # CRUD testimoni
│   ├── pengaturan.php     # Settings & backup
│   ├── login.php          # Login admin
│   └── logout.php         # Logout admin
├── config/                # Konfigurasi sistem
│   └── config.php         # Database & functions
├── database/              # Database schema
│   └── airfazza_db.sql    # SQL dump
├── uploads/               # File uploads
│   ├── products/          # Gambar produk
│   ├── gallery/           # Foto galeri
│   └── testimonials/      # Foto testimoni
├── backups/               # Database backups
├── assets/                # CSS, JS, images
├── includes/              # Header & footer website
├── index.php              # Halaman beranda
├── produk.php             # Halaman produk
├── profil.php             # Halaman profil
├── galeri.php             # Halaman galeri
├── kontak.php             # Halaman kontak
└── README.md              # Dokumentasi
```

## 🔒 Keamanan

- ✅ **CSRF Protection**: Token validation pada semua form
- ✅ **SQL Injection Prevention**: Prepared statements
- ✅ **XSS Protection**: Input sanitization
- ✅ **File Upload Security**: Type & size validation
- ✅ **Admin Authentication**: Session-based login
- ✅ **Directory Protection**: .htaccess files

## 🚀 Fitur Unggulan

### Interactive Rating System
- Click-to-rate dengan visual feedback
- Live preview testimoni
- Rating statistics dan average

### Advanced Image Management
- Drag & drop upload
- Real-time preview
- Automatic resizing
- Multiple format support

### Email Integration
- Template email untuk reply pesan
- Auto-generate email links
- SMTP ready

### Database Backup & Restore
- One-click backup creation
- Automatic backup before restore
- Download backup files
- Secure file handling

## 📊 Dashboard Analytics

- Total produk aktif
- Pesan masuk (dengan badge unread)
- Foto galeri
- Testimoni dengan rating rata-rata
- Chart statistik pesan 6 bulan terakhir
- Quick actions untuk tugas umum

## 🎨 Design System

### Colors
- **Primary**: Blue (#3B82F6)
- **Secondary**: Gray (#6B7280)
- **Success**: Green (#10B981)
- **Warning**: Yellow (#F59E0B)
- **Error**: Red (#EF4444)

### Typography
- **Headings**: Poppins (Google Fonts)
- **Body**: Inter (Google Fonts)
- **Code**: Menlo, Monaco, monospace

### Components
- Modern card designs dengan shadows
- Gradient backgrounds untuk hero sections
- Consistent button styles
- Interactive hover states
- Responsive grid layouts

## 🔄 Update & Maintenance

### Regular Tasks
1. **Backup Database**: Gunakan fitur backup di admin
2. **Update Content**: Kelola konten melalui admin dashboard
3. **Monitor Messages**: Cek pesan masuk secara berkala
4. **Review Testimonials**: Approve testimoni baru

### Security Updates
1. Ganti password admin secara berkala
2. Update PHP dan MySQL ke versi terbaru
3. Monitor file uploads untuk konten berbahaya
4. Review log akses secara berkala

## 📞 Support

Untuk bantuan teknis atau pertanyaan:
- **Email**: <EMAIL>
- **Documentation**: Lihat file README.md
- **Testing**: Gunakan test_system.php

## 📄 License

Copyright © 2024 Agen Air Fazza. All rights reserved.

---

**🎉 Website siap digunakan! Selamat mengelola bisnis air Anda dengan sistem yang modern dan profesional.**
