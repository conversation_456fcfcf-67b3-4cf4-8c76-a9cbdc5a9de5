<?php
$page_title = "Pengaturan Website";
$breadcrumb = '<li><a href="index.php" class="text-primary-600 hover:text-primary-700">Dashboard</a></li>
               <li><span class="mx-2">/</span></li>
               <li class="text-gray-500">Pengaturan</li>';

require_once 'includes/header.php';

$success_message = '';
$error_message = '';
$action = $_GET['action'] ?? 'general';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid.';
    } else {
        if ($action === 'general') {
            // Update general settings
            $settings = [
                'site_name' => sanitizeInput($_POST['site_name'] ?? ''),
                'site_description' => sanitizeInput($_POST['site_description'] ?? ''),
                'alamat' => sanitizeInput($_POST['alamat'] ?? ''),
                'telepon' => sanitizeInput($_POST['telepon'] ?? ''),
                'email' => sanitizeInput($_POST['email'] ?? ''),
                'jam_operasional' => sanitizeInput($_POST['jam_operasional'] ?? ''),
                'maps_embed' => $_POST['maps_embed'] ?? ''
            ];
            
            $updated = 0;
            foreach ($settings as $key => $value) {
                if (updateSiteSetting($key, $value)) {
                    $updated++;
                }
            }
            
            if ($updated > 0) {
                $success_message = 'Pengaturan berhasil disimpan!';
            } else {
                $error_message = 'Tidak ada perubahan yang disimpan.';
            }
            
        } elseif ($action === 'backup') {
            // Handle backup
            if (isset($_POST['create_backup'])) {
                $backup_result = createDatabaseBackup();
                if ($backup_result['success']) {
                    $success_message = 'Backup database berhasil dibuat! File: ' . $backup_result['filename'];
                } else {
                    $error_message = 'Gagal membuat backup: ' . $backup_result['message'];
                }
            }
            
        } elseif ($action === 'restore') {
            // Handle restore
            if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
                $restore_result = restoreDatabaseBackup($_FILES['backup_file']);
                if ($restore_result['success']) {
                    $success_message = 'Database berhasil direstore!';
                } else {
                    $error_message = 'Gagal restore database: ' . $restore_result['message'];
                }
            } else {
                $error_message = 'File backup tidak valid.';
            }
        }
    }
}

// Get current settings
$current_settings = [
    'site_name' => getSiteSetting('site_name', 'Agen Air Fazza'),
    'site_description' => getSiteSetting('site_description', 'Penyedia air bersih berkualitas tinggi'),
    'alamat' => getSiteSetting('alamat', ''),
    'telepon' => getSiteSetting('telepon', ''),
    'email' => getSiteSetting('email', ''),
    'jam_operasional' => getSiteSetting('jam_operasional', ''),
    'maps_embed' => getSiteSetting('maps_embed', '')
];

// Get backup files
$backup_files = getBackupFiles();
?>

<!-- Messages -->
<?php if ($success_message): ?>
    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <?php echo $success_message; ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 alert-auto-hide">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div><?php echo $error_message; ?></div>
        </div>
    </div>
<?php endif; ?>

<!-- Settings Navigation -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-6">
    <div class="p-6">
        <nav class="flex space-x-8">
            <a href="?action=general" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors <?php echo $action === 'general' ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'; ?>">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Pengaturan Umum
            </a>
            <a href="?action=backup" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors <?php echo $action === 'backup' ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'; ?>">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Backup Database
            </a>
            <a href="?action=restore" 
               class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors <?php echo $action === 'restore' ? 'bg-primary-100 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'; ?>">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Restore Database
            </a>
        </nav>
    </div>
</div>

<?php if ($action === 'general'): ?>
    <!-- General Settings -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
            <h2 class="text-xl font-semibold text-gray-900">Pengaturan Umum Website</h2>
            <p class="text-gray-600 mt-1">Kelola informasi dasar dan kontak perusahaan</p>
        </div>
        
        <form method="POST" data-validate class="p-6">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Site Name -->
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Website
                        </label>
                        <input type="text" 
                               id="site_name" 
                               name="site_name" 
                               value="<?php echo htmlspecialchars($current_settings['site_name']); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Agen Air Fazza">
                    </div>
                    
                    <!-- Site Description -->
                    <div>
                        <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Website
                        </label>
                        <textarea id="site_description" 
                                  name="site_description" 
                                  rows="3" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Deskripsi singkat tentang perusahaan..."><?php echo htmlspecialchars($current_settings['site_description']); ?></textarea>
                    </div>
                    
                    <!-- Address -->
                    <div>
                        <label for="alamat" class="block text-sm font-medium text-gray-700 mb-2">
                            Alamat Perusahaan
                        </label>
                        <textarea id="alamat" 
                                  name="alamat" 
                                  rows="3" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Alamat lengkap perusahaan..."><?php echo htmlspecialchars($current_settings['alamat']); ?></textarea>
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label for="telepon" class="block text-sm font-medium text-gray-700 mb-2">
                            Nomor Telepon
                        </label>
                        <input type="text" 
                               id="telepon" 
                               name="telepon" 
                               value="<?php echo htmlspecialchars($current_settings['telepon']); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="0812-3456-7890">
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Perusahaan
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="<?php echo htmlspecialchars($current_settings['email']); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <!-- Operating Hours -->
                    <div>
                        <label for="jam_operasional" class="block text-sm font-medium text-gray-700 mb-2">
                            Jam Operasional
                        </label>
                        <input type="text" 
                               id="jam_operasional" 
                               name="jam_operasional" 
                               value="<?php echo htmlspecialchars($current_settings['jam_operasional']); ?>"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                               placeholder="Senin - Sabtu: 08.00 - 17.00 WIB">
                    </div>
                    
                    <!-- Google Maps Embed -->
                    <div>
                        <label for="maps_embed" class="block text-sm font-medium text-gray-700 mb-2">
                            Google Maps Embed Code
                        </label>
                        <textarea id="maps_embed" 
                                  name="maps_embed" 
                                  rows="4" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="<iframe src=&quot;https://www.google.com/maps/embed?...&quot;></iframe>"><?php echo htmlspecialchars($current_settings['maps_embed']); ?></textarea>
                        <p class="text-sm text-gray-500 mt-2">
                            Dapatkan embed code dari Google Maps dan paste di sini.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                <button type="submit" class="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                    Simpan Pengaturan
                </button>
            </div>
        </form>
    </div>

<?php elseif ($action === 'backup'): ?>
    <!-- Backup Database -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Create Backup -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-xl font-semibold text-gray-900">Buat Backup Database</h2>
                <p class="text-gray-600 mt-1">Export seluruh data website ke file SQL</p>
            </div>

            <div class="p-6">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Yang akan di-backup:</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Semua data produk dan gambar</li>
                            <li>• Konten profil perusahaan</li>
                            <li>• Galeri foto dan kategori</li>
                            <li>• Pesan kontak dan balasan</li>
                            <li>• Testimoni pelanggan</li>
                            <li>• Pengaturan website</li>
                        </ul>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Penting:</h4>
                                <p class="text-sm text-yellow-700 mt-1">
                                    Backup hanya menyimpan data database. File gambar perlu di-backup secara terpisah.
                                </p>
                            </div>
                        </div>
                    </div>

                    <button type="submit"
                            name="create_backup"
                            class="w-full bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        Buat Backup Sekarang
                    </button>
                </form>
            </div>
        </div>

        <!-- Backup History -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-xl font-semibold text-gray-900">Riwayat Backup</h2>
                <p class="text-gray-600 mt-1">File backup yang tersedia untuk download</p>
            </div>

            <div class="p-6">
                <?php if ($backup_files): ?>
                    <div class="space-y-3">
                        <?php foreach ($backup_files as $file): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900"><?php echo $file['name']; ?></div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo formatFileSize($file['size']); ?> •
                                            <?php echo formatDate($file['date']); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="backup.php?download=<?php echo urlencode($file['name']); ?>"
                                       class="text-primary-600 hover:text-primary-700 p-2 rounded-lg hover:bg-primary-50 transition-colors"
                                       data-tooltip="Download">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </a>
                                    <a href="backup.php?delete=<?php echo urlencode($file['name']); ?>"
                                       class="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                                       data-tooltip="Hapus"
                                       data-confirm-delete="Yakin ingin menghapus backup '<?php echo $file['name']; ?>'?">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500">Belum ada file backup</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

<?php elseif ($action === 'restore'): ?>
    <!-- Restore Database -->
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6 border-b border-gray-100">
                <h2 class="text-xl font-semibold text-gray-900">Restore Database</h2>
                <p class="text-gray-600 mt-1">Import data dari file backup SQL</p>
            </div>

            <div class="p-6">
                <!-- Warning -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <h4 class="text-sm font-medium text-red-800">PERINGATAN!</h4>
                            <p class="text-sm text-red-700 mt-1">
                                Proses restore akan mengganti SEMUA data yang ada saat ini.
                                Pastikan Anda sudah membuat backup terlebih dahulu.
                            </p>
                        </div>
                    </div>
                </div>

                <form method="POST" enctype="multipart/form-data" data-validate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="space-y-6">
                        <!-- File Upload -->
                        <div>
                            <label for="backup_file" class="block text-sm font-medium text-gray-700 mb-2">
                                File Backup SQL <span class="text-red-500">*</span>
                            </label>
                            <input type="file"
                                   id="backup_file"
                                   name="backup_file"
                                   accept=".sql"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="text-sm text-gray-500 mt-2">
                                Hanya file dengan ekstensi .sql yang diperbolehkan.
                            </p>
                        </div>

                        <!-- Confirmation -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <label class="flex items-start">
                                <input type="checkbox"
                                       id="confirm_restore"
                                       required
                                       class="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <span class="ml-3 text-sm text-gray-700">
                                    Saya memahami bahwa proses restore akan mengganti semua data yang ada.
                                    Saya sudah membuat backup data saat ini.
                                </span>
                            </label>
                        </div>

                        <!-- Process Steps -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Proses Restore:</h4>
                            <ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                                <li>File SQL akan divalidasi</li>
                                <li>Database akan di-backup otomatis</li>
                                <li>Data lama akan dihapus</li>
                                <li>Data baru akan diimport</li>
                                <li>Sistem akan restart otomatis</li>
                            </ol>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-100">
                        <a href="?action=backup" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                            Batal
                        </a>
                        <button type="submit"
                                class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                onclick="return confirm('YAKIN ingin melakukan restore? Semua data saat ini akan diganti!')">
                            <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Restore Database
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php require_once 'includes/footer.php'; ?>
