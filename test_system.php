<?php
require_once 'config/config.php';

echo "<h1>🧪 Test Sistem Agen Air Fazza</h1>";

// Test database connection
echo "<h2>📊 Test Database Connection</h2>";
try {
    $test_query = $pdo->query("SELECT COUNT(*) as total FROM admin");
    $result = $test_query->fetch();
    echo "✅ Database connection: OK (Admin count: " . $result['total'] . ")<br>";
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "<br>";
}

// Test tables
echo "<h2>🗃️ Test Database Tables</h2>";
$tables = ['admin', 'produk', 'profil', 'galeri', 'pesan_kontak', 'testimoni', 'pengaturan'];

foreach ($tables as $table) {
    try {
        $count = fetchSingle("SELECT COUNT(*) as total FROM {$table}")['total'];
        echo "✅ Table '{$table}': OK ({$count} records)<br>";
    } catch (Exception $e) {
        echo "❌ Table '{$table}': FAILED - " . $e->getMessage() . "<br>";
    }
}

// Test upload directories
echo "<h2>📁 Test Upload Directories</h2>";
$upload_dirs = [
    'uploads' => UPLOADS_PATH,
    'uploads/products' => UPLOAD_PRODUCT_PATH,
    'uploads/gallery' => UPLOAD_GALLERY_PATH,
    'uploads/testimonials' => UPLOAD_TESTIMONIAL_PATH,
    'backups' => BACKUP_PATH
];

foreach ($upload_dirs as $name => $path) {
    if (file_exists($path) && is_writable($path)) {
        echo "✅ Directory '{$name}': OK (writable)<br>";
    } elseif (file_exists($path)) {
        echo "⚠️ Directory '{$name}': EXISTS but not writable<br>";
    } else {
        echo "❌ Directory '{$name}': NOT EXISTS<br>";
    }
}

// Test functions
echo "<h2>⚙️ Test Core Functions</h2>";

// Test sanitizeInput
$test_input = "<script>alert('test')</script>";
$sanitized = sanitizeInput($test_input);
echo "✅ sanitizeInput(): " . ($sanitized !== $test_input ? "OK" : "FAILED") . "<br>";

// Test generateCSRFToken
$token = generateCSRFToken();
echo "✅ generateCSRFToken(): " . (!empty($token) ? "OK" : "FAILED") . "<br>";

// Test verifyCSRFToken
$verify = verifyCSRFToken($token);
echo "✅ verifyCSRFToken(): " . ($verify ? "OK" : "FAILED") . "<br>";

// Test formatCurrency
$formatted = formatCurrency(15000);
echo "✅ formatCurrency(): " . ($formatted === 'Rp 15.000' ? "OK" : "FAILED ({$formatted})") . "<br>";

// Test timeAgo
$time_ago = timeAgo(date('Y-m-d H:i:s', strtotime('-1 hour')));
echo "✅ timeAgo(): " . (!empty($time_ago) ? "OK" : "FAILED") . "<br>";

// Test site settings
echo "<h2>🔧 Test Site Settings</h2>";
$site_name = getSiteSetting('site_name', 'Default');
echo "✅ getSiteSetting(): " . (!empty($site_name) ? "OK ({$site_name})" : "FAILED") . "<br>";

// Test backup functions
echo "<h2>💾 Test Backup Functions</h2>";
$backup_files = getBackupFiles();
echo "✅ getBackupFiles(): OK (" . count($backup_files) . " files)<br>";

echo "<h2>🎉 Test Summary</h2>";
echo "<p><strong>Sistem siap digunakan!</strong></p>";
echo "<p>Akses admin: <a href='admin/login.php'>admin/login.php</a></p>";
echo "<p>Username: admin | Password: password</p>";
echo "<p>Website: <a href='index.php'>index.php</a></p>";

// Clean up
if (file_exists(__FILE__)) {
    echo "<br><small>⚠️ Hapus file ini setelah testing selesai untuk keamanan.</small>";
}
?>
