<?php
$page_title = "Kontak Kami";
$page_description = "Hubungi Agen Air Fazza untuk informasi produk, pemesanan, atau pertanyaan lainnya. Kami siap melayani Anda";

require_once 'includes/header.php';

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Token keamanan tidak valid. Silakan coba lagi.';
    } else {
        // Sanitize input
        $nama = sanitizeInput($_POST['nama'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $subjek = sanitizeInput($_POST['subjek'] ?? '');
        $pesan = sanitizeInput($_POST['pesan'] ?? '');
        
        // Validation
        $errors = [];
        
        if (empty($nama)) {
            $errors[] = 'Nama wajib diisi';
        }
        
        if (empty($email)) {
            $errors[] = 'Email wajib diisi';
        } elseif (!validateEmail($email)) {
            $errors[] = 'Format email tidak valid';
        }
        
        if (empty($subjek)) {
            $errors[] = 'Subjek wajib diisi';
        }
        
        if (empty($pesan)) {
            $errors[] = 'Pesan wajib diisi';
        }
        
        if (empty($errors)) {
            // Save to database
            $data = [
                'nama_pengirim' => $nama,
                'email_pengirim' => $email,
                'subjek' => $subjek,
                'isi_pesan' => $pesan,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'status' => 'baru'
            ];
            
            if (insertData('pesan_kontak', $data)) {
                $success_message = 'Pesan Anda berhasil dikirim! Kami akan segera merespons.';
                // Clear form data
                $nama = $email = $subjek = $pesan = '';
            } else {
                $error_message = 'Terjadi kesalahan saat mengirim pesan. Silakan coba lagi.';
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Get pre-filled product name if coming from product page
$selected_product = isset($_GET['produk']) ? sanitizeInput($_GET['produk']) : '';

// Get contact settings
$alamat = getSiteSetting('alamat', 'Jl. Contoh No. 123, Kota Contoh');
$telepon = getSiteSetting('telepon', '0812-3456-7890');
$email_perusahaan = getSiteSetting('email', '<EMAIL>');
$jam_operasional = getSiteSetting('jam_operasional', 'Senin - Sabtu: 08.00 - 17.00 WIB');
$maps_embed = getSiteSetting('maps_embed', '');
?>

<!-- Page Header -->
<section class="relative py-20 bg-gradient-to-r from-primary-600 to-secondary-600 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-pattern"></div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-10 right-10 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-bounce-soft"></div>
    <div class="absolute top-32 left-20 w-12 h-12 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 right-32 w-8 h-8 bg-white bg-opacity-10 rounded-full animate-bounce-soft" style="animation-delay: 2s;"></div>
    
    <div class="relative z-10 container mx-auto px-4 text-center text-white">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6" data-animate="fade-in">
            Kontak Kami
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" data-animate="slide-up">
            Hubungi kami untuk informasi produk, pemesanan, atau pertanyaan lainnya. Kami siap melayani Anda
        </p>
        
        <!-- Breadcrumb -->
        <nav class="mt-8" data-animate="slide-up">
            <ol class="flex items-center justify-center space-x-2 text-blue-100">
                <li><a href="<?php echo SITE_URL; ?>" class="hover:text-white transition-colors">Beranda</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-white">Kontak</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div data-animate="slide-up">
                <div class="bg-white rounded-2xl shadow-soft p-8">
                    <h2 class="text-3xl font-heading font-bold text-gray-900 mb-6">
                        Kirim <span class="text-gradient">Pesan</span>
                    </h2>
                    
                    <!-- Success/Error Messages -->
                    <?php if ($success_message): ?>
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo $success_message; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div><?php echo $error_message; ?></div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" data-validate class="space-y-6">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Name Field -->
                        <div>
                            <label for="nama" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Lengkap <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="nama" 
                                   name="nama" 
                                   value="<?php echo htmlspecialchars($nama ?? ''); ?>"
                                   required 
                                   class="input-field"
                                   placeholder="Masukkan nama lengkap Anda">
                        </div>
                        
                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                   required 
                                   class="input-field"
                                   placeholder="<EMAIL>">
                        </div>
                        
                        <!-- Subject Field -->
                        <div>
                            <label for="subjek" class="block text-sm font-medium text-gray-700 mb-2">
                                Subjek <span class="text-red-500">*</span>
                            </label>
                            <select id="subjek" name="subjek" required class="select-field">
                                <option value="">Pilih subjek pesan</option>
                                <option value="Informasi Produk" <?php echo ($subjek ?? '') === 'Informasi Produk' ? 'selected' : ''; ?>>Informasi Produk</option>
                                <option value="Pemesanan" <?php echo ($subjek ?? '') === 'Pemesanan' ? 'selected' : ''; ?>>Pemesanan</option>
                                <option value="Keluhan" <?php echo ($subjek ?? '') === 'Keluhan' ? 'selected' : ''; ?>>Keluhan</option>
                                <option value="Saran" <?php echo ($subjek ?? '') === 'Saran' ? 'selected' : ''; ?>>Saran</option>
                                <option value="Lainnya" <?php echo ($subjek ?? '') === 'Lainnya' ? 'selected' : ''; ?>>Lainnya</option>
                            </select>
                        </div>
                        
                        <!-- Message Field -->
                        <div>
                            <label for="pesan" class="block text-sm font-medium text-gray-700 mb-2">
                                Pesan <span class="text-red-500">*</span>
                            </label>
                            <textarea id="pesan" 
                                      name="pesan" 
                                      rows="5" 
                                      required 
                                      class="textarea-field"
                                      placeholder="<?php echo $selected_product ? "Saya tertarik dengan produk: $selected_product. " : ''; ?>Tulis pesan Anda di sini..."><?php echo htmlspecialchars($pesan ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="btn-primary w-full">
                            <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Kirim Pesan
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div data-animate="slide-up" style="animation-delay: 0.2s;">
                <div class="space-y-8">
                    <!-- Contact Info Cards -->
                    <div class="bg-white rounded-2xl shadow-soft p-8">
                        <h3 class="text-2xl font-heading font-bold text-gray-900 mb-6">
                            Informasi <span class="text-gradient">Kontak</span>
                        </h3>
                        
                        <div class="space-y-6">
                            <!-- Address -->
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Alamat</h4>
                                    <p class="text-gray-600"><?php echo htmlspecialchars($alamat); ?></p>
                                </div>
                            </div>
                            
                            <!-- Phone -->
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Telepon</h4>
                                    <p class="text-gray-600">
                                        <a href="tel:<?php echo $telepon; ?>" class="hover:text-primary-600 transition-colors">
                                            <?php echo htmlspecialchars($telepon); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Email -->
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Email</h4>
                                    <p class="text-gray-600">
                                        <a href="mailto:<?php echo $email_perusahaan; ?>" class="hover:text-primary-600 transition-colors">
                                            <?php echo htmlspecialchars($email_perusahaan); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Operating Hours -->
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Jam Operasional</h4>
                                    <p class="text-gray-600"><?php echo htmlspecialchars($jam_operasional); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Contact Buttons -->
                    <div class="bg-white rounded-2xl shadow-soft p-8">
                        <h3 class="text-xl font-heading font-bold text-gray-900 mb-6">
                            Kontak <span class="text-gradient">Cepat</span>
                        </h3>
                        
                        <div class="space-y-4">
                            <!-- WhatsApp Button -->
                            <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $telepon); ?>?text=Halo, saya tertarik dengan produk Agen Air Fazza" 
                               target="_blank"
                               class="flex items-center justify-center w-full bg-green-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-green-600 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                                Chat WhatsApp
                            </a>
                            
                            <!-- Call Button -->
                            <a href="tel:<?php echo $telepon; ?>" 
                               class="flex items-center justify-center w-full bg-blue-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-blue-600 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Telepon Sekarang
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12" data-animate="slide-up">
            <h2 class="text-4xl md:text-5xl font-heading font-bold text-gray-900 mb-6">
                Lokasi <span class="text-gradient">Kami</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Kunjungi langsung lokasi kami atau gunakan peta di bawah untuk mendapatkan petunjuk arah
            </p>
        </div>
        
        <div class="rounded-2xl overflow-hidden shadow-soft" data-animate="slide-up">
            <?php if (!empty($maps_embed)): ?>
                <!-- Google Maps Embed -->
                <div class="aspect-w-16 aspect-h-9">
                    <?php echo $maps_embed; ?>
                </div>
            <?php else: ?>
                <!-- Placeholder Map -->
                <div class="aspect-w-16 aspect-h-9 bg-gray-200 flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <p class="text-lg font-medium">Peta akan segera tersedia</p>
                        <p class="text-sm">Hubungi kami untuk petunjuk arah yang lebih detail</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php
$additional_js = "
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pre-fill subject if coming from product page
    const urlParams = new URLSearchParams(window.location.search);
    const produk = urlParams.get('produk');
    
    if (produk) {
        const subjekSelect = document.getElementById('subjek');
        const pesanTextarea = document.getElementById('pesan');
        
        if (subjekSelect && pesanTextarea) {
            subjekSelect.value = 'Pemesanan';
            
            if (!pesanTextarea.value.includes(produk)) {
                pesanTextarea.value = `Saya tertarik dengan produk: ${produk}. ` + pesanTextarea.value;
            }
        }
    }
    
    // Form enhancement
    const form = document.querySelector('form[data-validate]');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitButton = form.querySelector('button[type=\"submit\"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<svg class=\"w-5 h-5 inline-block mr-2 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path></svg>Mengirim...';
                
                // Re-enable after 5 seconds to prevent permanent disable
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<svg class=\"w-5 h-5 inline-block mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"></path></svg>Kirim Pesan';
                }, 5000);
            }
        });
    }
});
</script>
";

require_once 'includes/footer.php';
?>
